import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User, Phone, MapPin, CreditCard, Building, Edit2, Check, X } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  mobile_number: string;
  village: string | null;
  cif_id: string | null;
}

interface SbAccount {
  id: string;
  sb_account_number: string;
  account_type: string;
  status: string;
  client_sb_accounts: {
    role: string;
    client_id: string;
    clients: {
      id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

interface ClientDetailsCardProps {
  client: Client | null;
  title: string;
  cifId?: string | null;
  onCifIdUpdate?: (newCifId: string) => void;
  isEditable?: boolean;
}

const ClientDetailsCard: React.FC<ClientDetailsCardProps> = ({
  client,
  title,
  cifId,
  onCifIdUpdate,
  isEditable = false
}) => {
  const [accounts, setAccounts] = useState<SbAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingCif, setEditingCif] = useState(false);
  const [tempCifId, setTempCifId] = useState(cifId || '');

  useEffect(() => {
    if (client) {
      fetchClientAccounts();
    } else {
      setAccounts([]);
    }
  }, [client]);

  useEffect(() => {
    setTempCifId(cifId || '');
  }, [cifId]);

  const fetchClientAccounts = async () => {
    if (!client) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('sb_accounts')
        .select(`
          id,
          sb_account_number,
          account_type,
          status,
          client_sb_accounts (
            role,
            client_id,
            clients (
              id,
              first_name,
              last_name
            )
          )
        `)
        .eq('is_deleted', false)
        .eq('status', 'active');

      if (error) throw error;

      // Filter accounts that belong to this client
      const clientAccounts = (data || []).filter(account =>
        account.client_sb_accounts.some(csa => csa.client_id === client.id)
      );

      setAccounts(clientAccounts);
    } catch (error) {
      console.error('Error fetching client accounts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCifIdSave = async () => {
    if (!client) return;

    try {
      // Update client's CIF ID in database
      const { error } = await supabase
        .from('clients')
        .update({ cif_id: tempCifId || null })
        .eq('id', client.id);

      if (error) throw error;

      // Update the client object locally
      client.cif_id = tempCifId || null;

      // Call the callback to update parent component state
      if (onCifIdUpdate) {
        onCifIdUpdate(tempCifId);
      }

      setEditingCif(false);

      toast({
        title: "Success",
        description: "CIF ID updated successfully",
      });
    } catch (error) {
      console.error('Error updating CIF ID:', error);
      toast({
        title: "Error",
        description: "Failed to update CIF ID",
        variant: "destructive",
      });
    }
  };

  const handleCifIdCancel = () => {
    setTempCifId(cifId || '');
    setEditingCif(false);
  };

  if (!client) {
    return (
      <Card className="bg-gray-50">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <User className="h-5 w-5 text-gray-400" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-center py-4">No client selected</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <User className="h-5 w-5 text-blue-600" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Client Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            <User className="h-4 w-4 text-gray-400" />
            <div>
              <p className="text-sm text-gray-500">Name</p>
              <p className="font-medium">{client.first_name} {client.last_name}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Phone className="h-4 w-4 text-gray-400" />
            <div>
              <p className="text-sm text-gray-500">Phone</p>
              <p className="font-medium">{client.mobile_number}</p>
            </div>
          </div>

          {client.village && (
            <div className="flex items-center gap-3">
              <MapPin className="h-4 w-4 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">Village</p>
                <p className="font-medium">{client.village}</p>
              </div>
            </div>
          )}

          <div className="flex items-center gap-3">
            <CreditCard className="h-4 w-4 text-gray-400" />
            <div className="flex-1">
              <p className="text-sm text-gray-500">CIF ID</p>
              {isEditable && editingCif ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={tempCifId}
                    onChange={(e) => setTempCifId(e.target.value)}
                    className="h-8 text-sm"
                    placeholder="Enter CIF ID"
                  />
                  <Button size="sm" onClick={handleCifIdSave} className="h-8 px-2">
                    <Check className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleCifIdCancel} className="h-8 px-2">
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <p className="font-medium">{cifId || client.cif_id || 'Not set'}</p>
                  {isEditable && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setEditingCif(true)}
                      className="h-6 w-6 p-0"
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Client Accounts */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-gray-400" />
            <Label className="text-sm font-medium">Accounts</Label>
          </div>

          {loading ? (
            <p className="text-sm text-gray-500">Loading accounts...</p>
          ) : accounts.length === 0 ? (
            <p className="text-sm text-gray-500">No accounts found</p>
          ) : (
            <div className="space-y-2">
              {accounts.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div>
                      <p className="font-medium text-sm">{account.sb_account_number}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {account.account_type}
                        </Badge>
                        <Badge variant={account.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                          {account.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {account.account_type === 'joint' && (
                    <div className="text-xs text-gray-500">
                      <p>Joint with:</p>
                      {account.client_sb_accounts
                        .filter(csa => csa.client_id !== client.id)
                        .map(csa => (
                          <p key={csa.client_id}>
                            {csa.clients.first_name} {csa.clients.last_name}
                          </p>
                        ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ClientDetailsCard;
