import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    // Parse request body to check for specific investment_id
    const body = await req.json().catch(() => ({}));
    const specificInvestmentId = body.investment_id;

    // Get notification settings including Twilio configuration
    const { data: settings } = await supabase
      .from('notification_settings')
      .select('alert_days_before, sms_enabled, twilio_account_sid, twilio_auth_token, twilio_phone_number')
      .single();

    if (!settings?.sms_enabled) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'SMS notifications disabled' 
      }), {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    if (!settings.twilio_account_sid || !settings.twilio_auth_token || !settings.twilio_phone_number) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Twilio configuration missing in database settings' 
      }), {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    const alertDaysBefore = settings.alert_days_before || 7;

    let targetDateStr = null;
    let investmentsQuery = supabase
      .from('investments')
      .select(`
        id,
        scheme_name,
        scheme_code,
        amount,
        maturity_amount,
        maturity_date,
        interest_rate,
        client_id,
        second_applicant_id,
        clients!investments_client_id_fkey(
          id,
          first_name,
          last_name,
          mobile_number,
          contact_person2,
          country_code
        ),
        secondary_applicant:clients!investments_second_applicant_id_fkey(
          id,
          first_name,
          last_name,
          mobile_number,
          contact_person2,
          country_code
        )
      `)
      .eq('status', 'active');

    // If specific investment ID is provided, fetch only that investment
    if (specificInvestmentId) {
      investmentsQuery = investmentsQuery.eq('id', specificInvestmentId);
    } else {
      // Otherwise, fetch investments maturing on target date
      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() + alertDaysBefore);
      targetDateStr = targetDate.toISOString().split('T')[0];
      investmentsQuery = investmentsQuery.eq('maturity_date', targetDateStr);
    }

    const { data: investments, error: investmentsError } = await investmentsQuery;

    if (investmentsError) {
      throw investmentsError;
    }

    let smsCount = 0;
    const results = [];

    for (const investment of investments || []) {
      // For specific investment requests, don't check for existing alerts (allow manual sending)
      if (!specificInvestmentId) {
        // Check if we haven't already sent an alert today (only for automated alerts)
        const { data: existingAlert } = await supabase
          .from('alerts')
          .select('id')
          .eq('investment_id', investment.id)
          .eq('alert_type', 'maturity_reminder')
          .eq('alert_date', new Date().toISOString().split('T')[0])
          .single();

        if (existingAlert) {
          continue; // Skip if alert already sent today
        }
      }

      // Collect all mobile numbers to send SMS to
      const mobileNumbers = [];
      
      // Primary applicant mobile
      if (investment.clients?.mobile_number && investment.clients?.country_code === '+91') {
        mobileNumbers.push({
          number: investment.clients.mobile_number,
          name: `${investment.clients.first_name} ${investment.clients.last_name}`,
          clientId: investment.clients.id,
          type: 'primary'
        });
      }

      // Primary applicant secondary contact
      if (investment.clients?.contact_person2 && investment.clients?.country_code === '+91') {
        mobileNumbers.push({
          number: investment.clients.contact_person2,
          name: `${investment.clients.first_name} ${investment.clients.last_name} (Secondary Contact)`,
          clientId: investment.clients.id,
          type: 'primary_secondary'
        });
      }

      // Secondary applicant mobile
      if (investment.secondary_applicant?.mobile_number && investment.secondary_applicant?.country_code === '+91') {
        mobileNumbers.push({
          number: investment.secondary_applicant.mobile_number,
          name: `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}`,
          clientId: investment.secondary_applicant.id,
          type: 'secondary'
        });
      }

      // Secondary applicant secondary contact
      if (investment.secondary_applicant?.contact_person2 && investment.secondary_applicant?.country_code === '+91') {
        mobileNumbers.push({
          number: investment.secondary_applicant.contact_person2,
          name: `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name} (Secondary Contact)`,
          clientId: investment.secondary_applicant.id,
          type: 'secondary_secondary'
        });
      }

      // Send SMS to each mobile number
      for (const mobile of mobileNumbers) {
        try {
          // Format mobile number for international format
          let formattedNumber = mobile.number.replace(/\D/g, '');
          if (formattedNumber.length === 10) {
            formattedNumber = '+91' + formattedNumber;
          } else if (!formattedNumber.startsWith('+')) {
            formattedNumber = '+' + formattedNumber;
          }

          // Create short SMS message for free version
          const message = `Investment Alert: Your investment in ${investment.scheme_name} (₹${investment.amount}) is maturing on ${investment.maturity_date}. Maturity amount: ₹${investment.maturity_amount}. Contact us for options. - Investment Team`;

          // Send SMS via Twilio
          const twilioResponse = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${settings.twilio_account_sid}/Messages.json`, {
            method: 'POST',
            headers: {
              'Authorization': 'Basic ' + btoa(`${settings.twilio_account_sid}:${settings.twilio_auth_token}`),
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              To: formattedNumber,
              From: settings.twilio_phone_number,
              Body: message
            })
          });

          const smsResult = await twilioResponse.json();
          const success = twilioResponse.ok;

          // Log the alert
          await supabase
            .from('alerts')
            .insert({
              investment_id: investment.id,
              client_id: mobile.clientId,
              alert_type: 'maturity_reminder',
              channel: 'sms',
              status: success ? 'sent' : 'failed',
              message: success ? `Maturity SMS sent to ${formattedNumber} (${mobile.type})` : `Failed to send SMS: ${smsResult.message || 'Unknown error'}`,
              alert_date: new Date().toISOString().split('T')[0]
            });

          if (success) {
            smsCount++;
          }

          results.push({
            investment_id: investment.id,
            mobile_number: formattedNumber,
            recipient_name: mobile.name,
            type: mobile.type,
            success: success,
            message: success ? 'SMS sent successfully' : smsResult.message || 'Failed to send SMS'
          });

        } catch (error) {
          console.error('Error sending SMS:', error);
          
          // Log failed alert
          await supabase
            .from('alerts')
            .insert({
              investment_id: investment.id,
              client_id: mobile.clientId,
              alert_type: 'maturity_reminder',
              channel: 'sms',
              status: 'failed',
              message: `Failed to send SMS: ${error.message}`,
              alert_date: new Date().toISOString().split('T')[0]
            });

          results.push({
            investment_id: investment.id,
            mobile_number: mobile.number,
            recipient_name: mobile.name,
            type: mobile.type,
            success: false,
            message: error.message
          });
        }
      }
    }

    const responseMessage = specificInvestmentId
      ? `Sent ${smsCount} SMS alerts for investment ${specificInvestmentId}`
      : `Processed ${smsCount} maturity SMS alerts`;

    return new Response(JSON.stringify({
      success: true,
      message: responseMessage,
      target_date: targetDateStr,
      alert_days_before: alertDaysBefore,
      sms_sent: smsCount,
      total_investments: investments?.length || 0,
      results: results
    }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });

  } catch (error) {
    console.error('Error in send-maturity-sms function:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });
  }
})
