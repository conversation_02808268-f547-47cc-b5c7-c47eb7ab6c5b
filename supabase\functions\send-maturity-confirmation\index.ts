import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS"
};

interface MaturityEmailRequest {
  investmentId: string;
  clientEmail: string;
  clientName: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const requestBody = await req.json();
    const { investmentId, clientEmail, clientName }: MaturityEmailRequest = requestBody;

    if (!investmentId || !clientEmail || !clientName) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Missing required fields'
      }), {
        status: 400,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    // Fetch investment details
    const { data: investment, error } = await supabase
      .from('investments')
      .select(`
        *,
        clients:clients!investments_client_id_fkey(first_name, last_name, email)
      `)
      .eq('id', investmentId)
      .single();

    if (error || !investment) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Investment not found'
      }), {
        status: 404,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    // Get notification settings from database
    const { data: settings } = await supabase
      .from('notification_settings')
      .select('sendgrid_api_key, sendgrid_sender_email')
      .single();

    const sendGridApiKey = settings?.sendgrid_api_key;
    const fromEmail = settings?.sendgrid_sender_email;

    if (!sendGridApiKey || !fromEmail) {
      return new Response(JSON.stringify({
        success: false,
        message: 'SendGrid configuration missing in database settings'
      }), {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

    const emailContent = generateMaturityConfirmationEmail(investment);

    const sendGridPayload = {
      personalizations: [{
        to: [{
          email: clientEmail,
          name: clientName
        }]
      }],
      from: {
        email: fromEmail,
        name: "Investment Management Team"
      },
      subject: `🎉 Investment Matured - ${investment.scheme_name}`,
      content: [{
        type: "text/html",
        value: emailContent
      }]
    };

    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${sendGridApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sendGridPayload)
    });

    if (response.ok) {
      // Log successful alert
      await supabase.from('alerts').insert({
        investment_id: investmentId,
        alert_type: 'maturity_confirmation',
        channel: 'email',
        status: 'sent',
        message: 'Maturity confirmation email sent successfully',
        alert_date: new Date().toISOString().split('T')[0]
      });

      return new Response(JSON.stringify({
        success: true,
        message: 'Maturity confirmation email sent successfully'
      }), {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    } else {
      const errorText = await response.text();
      
      // Log failed alert
      await supabase.from('alerts').insert({
        investment_id: investmentId,
        alert_type: 'maturity_confirmation',
        channel: 'email',
        status: 'failed',
        message: `SendGrid error: ${errorText}`,
        alert_date: new Date().toISOString().split('T')[0]
      });

      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to send email',
        error: errorText
      }), {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders }
      });
    }

  } catch (error: any) {
    console.error('Error sending maturity confirmation:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders }
    });
  }
};

function generateMaturityConfirmationEmail(investment: any) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Investment Maturity Confirmation</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; }
        .email-wrapper { max-width: 650px; margin: 0 auto; background: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { font-size: 32px; font-weight: 700; margin-bottom: 8px; }
        .content { padding: 40px 30px; }
        .success-badge { background: #dcfce7; color: #166534; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; display: inline-block; margin-bottom: 20px; }
        .investment-card { background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%); border-radius: 12px; padding: 25px; margin: 25px 0; border-left: 5px solid #10b981; }
        .detail-item { background: #ffffff; padding: 16px; border-radius: 8px; margin-bottom: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .detail-label { font-size: 12px; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px; }
        .detail-value { font-size: 16px; font-weight: 600; color: #1f2937; }
        .amount-highlight { color: #059669; font-size: 20px; }
        .cta-section { text-align: center; margin: 30px 0; padding: 25px; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 12px; }
        .footer { background: #1f2937; padding: 30px; text-align: center; color: #9ca3af; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        <div class="header">
          <h1>🎉 Investment Matured!</h1>
          <p>Congratulations on your successful investment</p>
        </div>
        
        <div class="content">
          <div class="success-badge">✅ Maturity Completed</div>
          
          <div style="font-size: 18px; color: #1f2937; margin-bottom: 20px;">
            Dear ${investment.clients.first_name} ${investment.clients.last_name},
          </div>
          
          <div style="font-size: 16px; color: #6b7280; margin-bottom: 30px; line-height: 1.6;">
            We're pleased to inform you that your investment has successfully matured today. Here are the final details:
          </div>
          
          <div class="investment-card">
            <div style="font-size: 20px; font-weight: 600; color: #1f2937; margin-bottom: 20px;">
              💰 Maturity Summary
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Scheme Name</div>
              <div class="detail-value">${investment.scheme_name}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Original Investment</div>
              <div class="detail-value">₹${investment.amount.toLocaleString()}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Maturity Amount</div>
              <div class="detail-value amount-highlight">₹${investment.maturity_amount.toLocaleString()}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Total Profit</div>
              <div class="detail-value amount-highlight">₹${(investment.maturity_amount - investment.amount).toLocaleString()}</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">Investment Period</div>
              <div class="detail-value">${new Date(investment.investment_date).toLocaleDateString('en-IN')} to ${new Date(investment.maturity_date).toLocaleDateString('en-IN')}</div>
            </div>
          </div>
          
          <div class="cta-section">
            <div style="font-size: 16px; color: #92400e; margin-bottom: 15px; font-weight: 500;">
              🚀 Ready for your next investment?
            </div>
            <div style="font-size: 14px; color: #92400e;">
              Contact us to explore new investment opportunities and continue growing your wealth.
            </div>
          </div>
          
          <div style="margin-top: 30px; font-size: 16px; color: #1f2937; line-height: 1.6;">
            Thank you for choosing us for your investment needs.<br>
            <strong style="color: #10b981;">Investment Management Team</strong>
          </div>
        </div>
        
        <div class="footer">
          <div style="color: #ffffff; font-weight: 600; font-size: 16px; margin-bottom: 8px;">Investment Pro</div>
          This is an automated maturity confirmation. For assistance, please contact our support team.
        </div>
      </div>
    </body>
    </html>
  `;
}

serve(handler);