import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

const schemeFormSchema = z.object({
  name: z.string().min(1, 'Scheme name is required'),
  scheme_code: z.string().min(1, 'Scheme code is required'),
  interest_rate: z.number().min(0.01, 'Interest rate must be greater than 0'),
  interest_type: z.enum(['simple', 'compound']),
  compounding_frequency: z.enum(['annually', 'semi-annually', 'quarterly', 'monthly']).optional(),
  payout_type: z.enum(['monthly', 'maturity']),
  tenure_months: z.number().min(1, 'Tenure must be at least 1 month'),
  lock_in_period_months: z.number().min(0, 'Lock-in period cannot be negative'),
  min_amount: z.number().min(1, 'Minimum amount is required'),
  max_amount: z.number().optional(),
  commission_percentage: z.number().min(0).max(100).optional(),
  supports_sip: z.boolean(),
  min_sip_amount: z.number().optional(),
  max_sip_amount: z.number().optional(),
  is_active: z.boolean(),
});

type SchemeFormData = z.infer<typeof schemeFormSchema>;

const SchemeForm: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const form = useForm<SchemeFormData>({
    resolver: zodResolver(schemeFormSchema),
    defaultValues: {
      name: '',
      scheme_code: '',
      interest_rate: 0,
      interest_type: 'simple',
      compounding_frequency: 'annually',
      payout_type: 'maturity',
      tenure_months: 12,
      lock_in_period_months: 0,
      min_amount: 0,
      max_amount: undefined,
      commission_percentage: 0,
      supports_sip: false,
      min_sip_amount: undefined,
      max_sip_amount: undefined,
      is_active: true,
    },
  });

  const watchInterestType = form.watch('interest_type');
  const watchSupportsSip = form.watch('supports_sip');

  const onSubmit = async (data: SchemeFormData) => {
    setLoading(true);
    try {
      // Validate SIP amounts if SIP is supported
      if (data.supports_sip) {
        if (data.min_sip_amount && data.max_sip_amount && data.min_sip_amount > data.max_sip_amount) {
          toast({
            title: "Error",
            description: "Minimum SIP amount cannot be greater than maximum SIP amount",
            variant: "destructive",
          });
          setLoading(false);
          return;
        }
      }

      // Validate max amount
      if (data.max_amount && data.min_amount > data.max_amount) {
        toast({
          title: "Error",
          description: "Minimum amount cannot be greater than maximum amount",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      const schemeData = {
        name: data.name,
        scheme_code: data.scheme_code,
        interest_rate: data.interest_rate,
        interest_type: data.interest_type,
        compounding_frequency: data.interest_type === 'compound' ? data.compounding_frequency : null,
        payout_type: data.payout_type,
        tenure_months: data.tenure_months,
        lock_in_period_months: data.lock_in_period_months,
        min_amount: data.min_amount,
        max_amount: data.max_amount || null,
        commission_percentage: data.commission_percentage || 0,
        supports_sip: data.supports_sip,
        min_sip_amount: data.supports_sip ? data.min_sip_amount : null,
        max_sip_amount: data.supports_sip ? data.max_sip_amount : null,
        is_active: data.is_active,
      };

      const { error } = await supabase
        .from('schemes')
        .insert(schemeData);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Scheme created successfully",
      });

      navigate('/schemes');
    } catch (error) {
      console.error('Error creating scheme:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create scheme",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/schemes')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Schemes</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Add New Scheme</h1>
        </div>
      </div>

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Scheme Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter scheme name" {...field} className="h-9" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="scheme_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Scheme Code *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter scheme code" {...field} className="h-9" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="interest_rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Interest Rate (%) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Enter rate"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) )}
                          className="h-9"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="interest_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Interest Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-9">
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="simple">Simple</SelectItem>
                          <SelectItem value="compound">Compound</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="payout_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Payout Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-9">
                            <SelectValue placeholder="Select payout" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="maturity">On Maturity</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {watchInterestType === 'compound' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="compounding_frequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Compounding Frequency *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-9">
                              <SelectValue placeholder="Select frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="annually">Annually</SelectItem>
                            <SelectItem value="semi-annually">Semi-annually</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Terms & Amount */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold">Terms & Amount</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <FormField
                  control={form.control}
                  name="tenure_months"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Tenure (Months) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Months"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                          className="h-9"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lock_in_period_months"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Lock-in Period</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Months"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) )}
                          className="h-9"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="min_amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Min Amount *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Amount"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) )}
                          className="h-9"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="max_amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Max Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Optional"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          className="h-9"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="commission_percentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">Commission Percentage</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Enter commission % (optional)"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          className="h-9"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* SIP Options & Status */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold">SIP Options</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="supports_sip"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Support SIP
                        </FormLabel>
                        <div className="text-xs text-muted-foreground">
                          Enable Systematic Investment Plan
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {watchSupportsSip && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <FormField
                      control={form.control}
                      name="min_sip_amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Min SIP Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Min amount"
                              {...field}
                              value={field.value || ''}
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                              className="h-9"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="max_sip_amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Max SIP Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Max amount"
                              {...field}
                              value={field.value || ''}
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                              className="h-9"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold">Status</CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Active Status
                        </FormLabel>
                        <div className="text-xs text-muted-foreground">
                          Make scheme active and available
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/schemes')}
              className="w-full sm:w-auto h-9"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="w-full sm:w-auto h-9"
            >
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Creating...' : 'Create Scheme'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SchemeForm;
