
import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Plus, ArrowLeft, ArrowRight, CheckCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { formatDisplayDate } from '@/utils/dateFormat';
import ClientSelector from '@/components/investment/ClientSelector';
import SchemeSelector from '@/components/investment/SchemeSelector';
import NomineeSelector from '@/components/investment/NomineeSelector';
import ClientDetailsCard from '@/components/investment/ClientDetailsCard';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  village: string | null;
  cif_id: string | null;
}

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  min_amount: number;
  max_amount: number;
  lock_in_period_months: number;
  commission_percentage: number;
  compounding_frequency: string;
  payout_type: string;
  supports_sip: boolean;
}

interface Nominee {
  id: string;
  name: string;
  relation: string;
}
interface OriginalInvestment {
  id: string;
  maturity_amount: number;
  scheme_name: string;
}
interface SbAccount {
  id: string;
  sb_account_number: string;
  account_type: string; // changed from: 'single' | 'joint'
  status: string;        // changed from: 'active' | 'inactive'
  client_sb_accounts: {
    role: string; // optional: narrow to 'primary' | 'secondary' if strict
    client_id: string;
    clients: {
      id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

interface PrefilledInvestmentState {
  prefilledData?: {
    client_id?: string;
    amount?: number;
    remark?: string;
    isReinvestment?: boolean;
    reinvestment_source_id?: string;
    originalInvestment?: OriginalInvestment;
  };
}

// Investment Form Schema
const investmentFormSchema = z.object({
  client_id: z.string().min(1, 'Primary client is required'),
  second_applicant_id: z.string().optional(),
  scheme_id: z.string().min(1, 'Investment scheme is required'),
  nominee_id: z.string().optional(),
  amount: z.string().min(1, 'Investment amount is required').refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, 'Investment amount must be a valid positive number'),
  investment_date: z.date({
    required_error: 'Investment date is required',
    invalid_type_error: 'Please select a valid investment date',
  }),
  remark: z.string().optional(),
});

// Transaction Form Schema
const transactionFormSchema = z.object({
  payment_mode: z.string().min(1, 'Payment mode is required'),
  reference_number: z.string().optional(),
  sb_account_id: z.string().optional(),
  new_account_number: z.string().optional(),
}).refine((data) => {
  // Reference number is required for cheque and ecs, optional for cash and rtgs/neft
  if ((data.payment_mode === 'cheque' || data.payment_mode === 'ecs') && !data.reference_number) {
    return false;
  }
  return true;
}, {
  message: 'Reference number is required for cheque and ECS payments',
  path: ['reference_number'],
});

type InvestmentFormData = z.infer<typeof investmentFormSchema>;
type TransactionFormData = z.infer<typeof transactionFormSchema>;


const InvestmentForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [tdsPercentage, setTdsPercentage] = useState(10);
  const [isReinvestment, setIsReinvestment] = useState(false);
  const [reinvestmentSourceId, setReinvestmentSourceId] = useState<string | null>(null);
  const [originalInvestment, setOriginalInvestment] = useState<OriginalInvestment | null>(null);


  // Investment Form state
  const [amountError, setAmountError] = useState<string | null>(null);
  const [liveErrors, setLiveErrors] = useState<Record<string, string>>({});
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedSecondaryApplicant, setSelectedSecondaryApplicant] = useState<Client | null>(null);
  const [selectedScheme, setSelectedScheme] = useState<Scheme | null>(null);
  const [selectedNominee, setSelectedNominee] = useState<Nominee | null>(null);
  const [primaryCifId, setPrimaryCifId] = useState<string>('');
  const [secondaryCifId, setSecondaryCifId] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [investmentDate, setInvestmentDate] = useState<Date>();
  const [maturityDate, setMaturityDate] = useState<Date>();
  const [maturityAmount, setMaturityAmount] = useState<number>(0);
  const [remark, setRemark] = useState('');
  const [commission, setCommission] = useState<number>(0);
  const [tdsAmount, setTdsAmount] = useState<number>(0);
  const [actualProfit, setActualProfit] = useState<number>(0);

  // Transaction Form state
  const [paymentMode, setPaymentMode] = useState('');
  const [referenceNumber, setReferenceNumber] = useState('');

  // Initialize forms
  const investmentForm = useForm<InvestmentFormData>({
    resolver: zodResolver(investmentFormSchema),
    defaultValues: {
      client_id: '',
      second_applicant_id: '',
      scheme_id: '',
      nominee_id: '',
      amount: '',
      investment_date: undefined,
      remark: '',
    },
  });

  const transactionForm = useForm<TransactionFormData>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      payment_mode: '',
      reference_number: '',
      sb_account_id: '',
      new_account_number: '',
    },
  });

  useEffect(() => {
    fetchTdsPercentage();

    // Handle pre-filled data from reinvestment
    const state = location.state as PrefilledInvestmentState;

    if (state?.prefilledData) {
      const { prefilledData } = state;
      if (prefilledData.client_id) {
        fetchClientById(prefilledData.client_id);
      }
      if (prefilledData.amount) setAmount(prefilledData.amount.toString());
      if (prefilledData.remark) setRemark(prefilledData.remark);
      if (prefilledData.isReinvestment) setIsReinvestment(true);
      if (prefilledData.reinvestment_source_id) setReinvestmentSourceId(prefilledData.reinvestment_source_id);
      if (prefilledData.originalInvestment) setOriginalInvestment(prefilledData.originalInvestment);
    }
  }, [location.state]);



  useEffect(() => {
    if (selectedScheme && amount && investmentDate) {
      calculateMaturityDetails();
    }
  }, [selectedScheme, amount, investmentDate]);

  // Update CIF IDs when clients change (only if not already set)
  useEffect(() => {
    if (selectedClient && !primaryCifId) {
      setPrimaryCifId(selectedClient.cif_id || '');
    } else if (!selectedClient) {
      setPrimaryCifId('');
    }
  }, [selectedClient, primaryCifId]);

  useEffect(() => {
    if (selectedSecondaryApplicant && !secondaryCifId) {
      setSecondaryCifId(selectedSecondaryApplicant.cif_id || '');
    } else if (!selectedSecondaryApplicant) {
      setSecondaryCifId('');
    }
  }, [selectedSecondaryApplicant, secondaryCifId]);

  const fetchClientById = async (clientId: string) => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, first_name, last_name, email, mobile_number, village, cif_id')
        .eq('id', clientId)
        .single();

      if (error) throw error;
      setSelectedClient(data);
    } catch (error) {
      console.error('Error fetching client:', error);
    }
  };

  const fetchTdsPercentage = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_settings')
        .select('tds_percentage')
        .limit(1)
        .single();

      if (error) throw error;
      if (data?.tds_percentage) {
        setTdsPercentage(data.tds_percentage);
      }
    } catch (error) {
      console.error('Error fetching TDS percentage:', error);
    }
  };





  const calculateMaturityDetails = () => {
    if (!selectedScheme || !amount || !investmentDate) return;

    const principal = parseFloat(amount);
    const rate = selectedScheme.interest_rate;
    const tenureMonths = selectedScheme.tenure_months;

    // Maturity Date
    const maturity = new Date(investmentDate);
    maturity.setMonth(maturity.getMonth() + tenureMonths);
    setMaturityDate(maturity);

    // Monthly interest rate
    const monthlyRate = rate / 100 / 12;

    // Maturity Amount
    const maturityAmt = principal + (principal * monthlyRate * tenureMonths);
    setMaturityAmount(maturityAmt);

    // Commission
    const commissionAmt = (principal * selectedScheme.commission_percentage) / 100;
    setCommission(commissionAmt);

    // TDS on commission
    const tds = (commissionAmt * tdsPercentage) / 100;
    setTdsAmount(tds);

    // Actual Profit = Commission - TDS
    const actualProfitAmt = commissionAmt - tds;
    setActualProfit(actualProfitAmt);
  };

  const generateReferenceNumber = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `INV-${timestamp}-${random}`;
  };

  // Format dates to avoid timezone issues
  const formatDateForDB = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const validateLiveFields = () => {
    const newLiveErrors: Record<string, string> = {};

    // Validate required fields
    if (!selectedClient) {
      newLiveErrors.client_id = 'Primary client is required';
    }
    if (!selectedScheme) {
      newLiveErrors.scheme_id = 'Investment scheme is required';
    }
    if (!amount || amount.trim() === '') {
      newLiveErrors.amount = 'Investment amount is required';
    } else {
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        newLiveErrors.amount = 'Investment amount must be a valid number';
      } else if (numericAmount < 0) {
        newLiveErrors.amount = 'Investment amount cannot be negative';
      } else if (numericAmount === 0) {
        newLiveErrors.amount = 'Investment amount must be greater than zero';
      } else if (selectedScheme) {
        if (numericAmount < selectedScheme.min_amount) {
          newLiveErrors.amount = `Amount must be at least ₹${selectedScheme.min_amount.toLocaleString()}`;
        } else if (selectedScheme.max_amount && numericAmount > selectedScheme.max_amount) {
          newLiveErrors.amount = `Amount cannot exceed ₹${selectedScheme.max_amount.toLocaleString()}`;
        }
      }
    }
    if (!investmentDate) {
      newLiveErrors.investment_date = 'Investment date is required';
    }

    setLiveErrors(newLiveErrors);
    return Object.keys(newLiveErrors).length === 0;
  };

  const handleNextStep = async () => {
    // Update form values from state
    investmentForm.setValue('client_id', selectedClient?.id || '');
    investmentForm.setValue('second_applicant_id', selectedSecondaryApplicant?.id || '');
    investmentForm.setValue('scheme_id', selectedScheme?.id || '');
    investmentForm.setValue('nominee_id', selectedNominee?.id || '');
    investmentForm.setValue('amount', amount);
    if (investmentDate) investmentForm.setValue('investment_date', investmentDate);
    investmentForm.setValue('remark', remark);

    // Validate live fields
    const isLiveValid = validateLiveFields();
    if (!isLiveValid) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before proceeding",
        variant: "destructive",
      });
      return;
    }

    // Validate form schema
    const isValid = await investmentForm.trigger();
    if (!isValid) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before proceeding",
        variant: "destructive",
      });
      return;
    }

    setCurrentStep(2);
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Update transaction form values from state
    transactionForm.setValue('payment_mode', paymentMode);
    transactionForm.setValue('reference_number', referenceNumber);

    // Validate transaction form
    const isTransactionValid = await transactionForm.trigger();
    if (!isTransactionValid) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before submitting",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {

      // If this is a reinvestment, create maturity payout transaction first
      if (isReinvestment && originalInvestment) {
        const { error: payoutError } = await supabase
          .from('transactions')
          .insert({
            investment_id: originalInvestment.id,
            amount_type: 'maturity_payout',
            amount: originalInvestment.maturity_amount,
            transaction_date: formatDateForDB(new Date()),
            reference_number: `MAT-${Date.now()}`,
            remark: `Maturity payout for reinvestment - ${originalInvestment.scheme_name}`
          });

        if (payoutError) throw payoutError;

        // Update original investment status
        const { error: updateError } = await supabase
          .from('investments')
          .update({ status: 'reinvested' })
          .eq('id', originalInvestment.id);

        if (updateError) throw updateError;
      }

      const investmentData = {
        client_id: selectedClient.id,
        second_applicant_id: selectedSecondaryApplicant?.id || null,
        scheme_id: selectedScheme.id,
        nominee_id: selectedNominee?.id || null,
        amount: parseFloat(amount),
        investment_date: formatDateForDB(investmentDate),
        start_date: null,
        maturity_date: maturityDate ? formatDateForDB(maturityDate) : null,
        maturity_amount: maturityAmount,
        investment_type: 'lumpsum',
        remark: remark || null,
        actual_profit: actualProfit,
        tds_amount: tdsAmount,
        reinvestment_source_id: reinvestmentSourceId,
        // Store CIF IDs from state (may be updated by user)
        primary_applicant_cif_id: primaryCifId || null,
        secondary_applicant_cif_id: secondaryCifId || null,
        // Store scheme details
        scheme_name: selectedScheme.name,
        scheme_code: selectedScheme.scheme_code,
        interest_rate: selectedScheme.interest_rate,
        interest_type: selectedScheme.interest_type,
        tenure_months: selectedScheme.tenure_months,
        min_amount: selectedScheme.min_amount,
        max_amount: selectedScheme.max_amount,
        lock_in_period_months: selectedScheme.lock_in_period_months,
        commission_percentage: selectedScheme.commission_percentage,
        compounding_frequency: selectedScheme.compounding_frequency,
        payout_status: selectedScheme.payout_type,
        supports_sip: selectedScheme.supports_sip,
        status: 'active'
      };

      const { data: investment, error } = await supabase
        .from('investments')
        .insert([investmentData])
        .select()
        .single();

      if (error) throw error;

      // If this is a reinvestment, update the source investment status
      if (isReinvestment && reinvestmentSourceId) {
        const { error: updateError } = await supabase
          .from('investments')
          .update({ status: 'reinvested' })
          .eq('id', reinvestmentSourceId);

        if (updateError) {
          console.error('Error updating source investment status:', updateError);
        }
      }

      // Create transaction record with user-provided details
      const transactionData = {
        investment_id: investment.id,
        amount_type: 'investment',
        amount: parseFloat(amount),
        transaction_date: formatDateForDB(investmentDate),
        reference_number: paymentMode === 'cash' ? null : (referenceNumber || null),
        payment_mode: paymentMode,
        sb_account_id: null, // No SB account for investment forms
        remark: isReinvestment ? `Reinvestment in ${selectedScheme.name}` : `Initial investment in ${selectedScheme.name}`
      };

      console.log('Creating transaction with data:', transactionData);

      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .insert([transactionData])
        .select()
        .single();

      if (transactionError) {
        console.error('Transaction creation error:', transactionError);
        throw new Error(`Failed to create transaction: ${transactionError.message}`);
      }

      console.log('Transaction created successfully:', transaction);

      toast({
        title: "Success",
        description: isReinvestment ? "Reinvestment and transaction created successfully" : "Investment and transaction created successfully",
      });

      navigate('/investments');
    } catch (error) {
      console.error('Error creating investment:', error);
      toast({
        title: "Error",
        description: "Failed to create investment",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const renderInvestmentForm = () => (
    <Form {...investmentForm}>
      <form onSubmit={(e) => { e.preventDefault(); handleNextStep(); }}>
        <Card>
          <CardHeader >
            <CardTitle>Investment Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
            {/* Client Selection Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <FormField
                control={investmentForm.control}
                name="client_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Primary Client *</FormLabel>
                    <FormControl>
                      <ClientSelector
                        selectedClient={selectedClient}
                        onClientSelect={(client) => {
                          setSelectedClient(client);
                          field.onChange(client?.id || '');
                          // Clear live error when client is selected
                          if (client) {
                            setLiveErrors(prev => {
                              const newErrors = { ...prev };
                              delete newErrors.client_id;
                              return newErrors;
                            });
                          }
                        }}
                        required
                        disabled={isReinvestment}
                      />
                    </FormControl>
                    <FormMessage />
                    {liveErrors.client_id && (
                      <p className="text-sm text-red-500 mt-1">{liveErrors.client_id}</p>
                    )}
                  </FormItem>
                )}
              />
              <FormField
                control={investmentForm.control}
                name="second_applicant_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Secondary Applicant (Optional)</FormLabel>
                    <FormControl>
                      <ClientSelector
                        selectedClient={selectedSecondaryApplicant}
                        onClientSelect={(client) => {
                          setSelectedSecondaryApplicant(client);
                          field.onChange(client?.id || '');
                        }}
                        required={false}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Client Details Cards */}
            {(selectedClient || selectedSecondaryApplicant) && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {selectedClient && (
                  <ClientDetailsCard
                    client={selectedClient}
                    title="Primary Applicant Details"
                    cifId={primaryCifId}
                    onCifIdUpdate={setPrimaryCifId}
                    isEditable={true}
                  />
                )}

                {selectedSecondaryApplicant && (
                  <ClientDetailsCard
                    client={selectedSecondaryApplicant}
                    title="Secondary Applicant Details"
                    cifId={secondaryCifId}
                    onCifIdUpdate={setSecondaryCifId}
                    isEditable={true}
                  />
                )}
              </div>
            )}

            {/* Scheme Selection */}
            <FormField
              control={investmentForm.control}
              name="scheme_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Investment Scheme *</FormLabel>
                  <FormControl>
                    <SchemeSelector
                      selectedScheme={selectedScheme}
                      onSchemeSelect={(scheme) => {
                        setSelectedScheme(scheme);
                        field.onChange(scheme?.id || '');
                        // Clear live error when scheme is selected
                        if (scheme) {
                          setLiveErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.scheme_id;
                            return newErrors;
                          });
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                  {liveErrors.scheme_id && (
                    <p className="text-sm text-red-500 mt-1">{liveErrors.scheme_id}</p>
                  )}
                </FormItem>
              )}
            />

            {/* Show scheme details if selected */}
            {selectedScheme && (
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <h4 className="font-medium">Scheme Details</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div><strong>Interest Rate:</strong> {selectedScheme.interest_rate}%</div>
                  <div><strong>Tenure:</strong> {selectedScheme.tenure_months} months</div>
                  <div><strong>Interest Type:</strong> {selectedScheme.interest_type}</div>
                  <div><strong>Min Amount:</strong> ₹{selectedScheme.min_amount?.toLocaleString()}</div>
                  <div><strong>Max Amount:</strong> ₹{selectedScheme.max_amount?.toLocaleString()}</div>
                  <div><strong>Commission:</strong> {selectedScheme.commission_percentage}%</div>
                </div>
              </div>
            )}

            {/* Amount and Investment Type Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <FormField
                control={investmentForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Investment Amount *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        onWheel={(e) => e.currentTarget.blur()}
                        placeholder="Enter investment amount"
                        value={amount}
                        onChange={(e) => {
                          const input = e.target.value;
                          setAmount(input);
                          field.onChange(input);

                          // Live validation for amount
                          const newLiveErrors = { ...liveErrors };
                          if (!input || input.trim() === '') {
                            newLiveErrors.amount = 'Investment amount is required';
                          } else {
                            const numericAmount = parseFloat(input);
                            if (isNaN(numericAmount)) {
                              newLiveErrors.amount = 'Investment amount must be a valid number';
                            } else if (numericAmount < 0) {
                              newLiveErrors.amount = 'Investment amount cannot be negative';
                            } else if (numericAmount === 0) {
                              newLiveErrors.amount = 'Investment amount must be greater than zero';
                            } else if (selectedScheme) {
                              if (numericAmount < selectedScheme.min_amount) {
                                newLiveErrors.amount = `Amount must be at least ₹${selectedScheme.min_amount.toLocaleString()}`;
                              } else if (selectedScheme.max_amount && numericAmount > selectedScheme.max_amount) {
                                newLiveErrors.amount = `Amount cannot exceed ₹${selectedScheme.max_amount.toLocaleString()}`;
                              } else {
                                delete newLiveErrors.amount;
                              }
                            } else {
                              delete newLiveErrors.amount;
                            }
                          }
                          setLiveErrors(newLiveErrors);
                        }}
                        disabled={isReinvestment}
                      />
                    </FormControl>
                    <FormMessage />
                    {liveErrors.amount && (
                      <p className="text-sm text-red-500 mt-1">{liveErrors.amount}</p>
                    )}
                  </FormItem>
                )}
              />
              <div className="space-y-2">
                <Label>Investment Type</Label>
                <Input value="Lumpsum" disabled />
              </div>
            </div>

            {/* Investment Date Field */}
            <FormField
              control={investmentForm.control}
              name="investment_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Investment Date *</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={investmentDate}
                      onSelect={(date) => {
                        setInvestmentDate(date);
                        field.onChange(date);
                        // Clear live error when date is selected
                        if (date) {
                          setLiveErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.investment_date;
                            return newErrors;
                          });
                        }
                      }}
                      placeholder="Select investment date"
                    />
                  </FormControl>
                  <FormMessage />
                  {liveErrors.investment_date && (
                    <p className="text-sm text-red-500 mt-1">{liveErrors.investment_date}</p>
                  )}
                </FormItem>
              )}
            />

            {/* Nominee Selection */}
            {selectedClient && (
              <FormField
                control={investmentForm.control}
                name="nominee_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nominee (Optional)</FormLabel>
                    <FormControl>
                      <NomineeSelector
                        clientId={selectedClient.id}
                        selectedNominee={selectedNominee}
                        onNomineeSelect={(nominee) => {
                          setSelectedNominee(nominee);
                          field.onChange(nominee?.id || '');
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Calculated Fields */}
            {maturityDate && (
              <div className="bg-blue-50 p-4 rounded-lg space-y-2">
                <h4 className="font-medium">Calculated Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div><strong>Maturity Date:</strong> {formatDisplayDate(maturityDate)}</div>
                  <div><strong>Maturity Amount:</strong> ₹{maturityAmount.toLocaleString()}</div>
                  <div><strong>Commission:</strong> ₹{commission.toLocaleString()}</div>
                  <div><strong>TDS Amount:</strong> ₹{tdsAmount.toLocaleString()}</div>
                  <div><strong>Net Commission:</strong> ₹{actualProfit.toLocaleString()}</div>
                </div>
              </div>
            )}

            {/* Remark */}
            <FormField
              control={investmentForm.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Remark (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any remarks"
                      value={remark}
                      onChange={(e) => {
                        setRemark(e.target.value);
                        field.onChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col sm:flex-row sm:justify-end gap-3 sm:gap-4 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/investments')}
                className="w-full sm:w-auto order-2 sm:order-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={Object.keys(liveErrors).length > 0}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                <ArrowRight className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Next: Transaction Details</span>
                <span className="sm:hidden">Next Step</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );

  const renderTransactionForm = () => (
    <Form {...transactionForm}>
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Transaction Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
            {/* Pre-filled Investment Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Investment Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Client:</strong> {selectedClient?.first_name} {selectedClient?.last_name}</div>
                <div><strong>Scheme:</strong> {selectedScheme?.name}</div>
                <div><strong>Amount:</strong> ₹{parseFloat(amount).toLocaleString()}</div>
                <div><strong>Type:</strong> Investment</div>
              </div>
            </div>

            {/* Payment Mode */}
            <FormField
              control={transactionForm.control}
              name="payment_mode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Mode *</FormLabel>
                  <FormControl>
                    <Select value={paymentMode} onValueChange={(value) => {
                      setPaymentMode(value);
                      field.onChange(value);
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment mode" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="rtgs/neft">RTGS/NEFT</SelectItem>
                        <SelectItem value="ecs">ECS</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />



            {/* Reference Number */}
            <FormField
              control={transactionForm.control}
              name="reference_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Reference Number / Cheque Number
                    {(paymentMode === 'cheque' || paymentMode === 'ecs') && ' *'}
                    {paymentMode === 'cash' && ' (Not Required)'}
                    {(paymentMode === 'rtgs/neft') && ' (Optional)'}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={
                        paymentMode === 'cash'
                          ? "Reference number not required for cash"
                          : paymentMode === 'cheque'
                            ? "Enter cheque number"
                            : paymentMode === 'ecs'
                              ? "Enter ECS reference number"
                              : "Enter reference number"
                      }
                      value={referenceNumber}
                      onChange={(e) => {
                        setReferenceNumber(e.target.value);
                        field.onChange(e.target.value);
                      }}
                      disabled={paymentMode === 'cash'}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-col sm:flex-row sm:justify-between gap-3 sm:gap-4 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(1)}
                className="w-full sm:w-auto order-2 sm:order-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Back to Investment</span>
                <span className="sm:hidden">Back</span>
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                {loading ? 'Creating...' : (
                  <>
                    <span className="hidden sm:inline">Create Investment</span>
                    <span className="sm:hidden">Create</span>
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header with Step Progress */}
      <div className="flex items-center gap-2 sm:gap-4">
        <Button variant="ghost" size="sm" onClick={() => navigate('/investments')} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1 min-w-0">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold truncate">
            {isReinvestment ? 'Create Reinvestment' : 'Add New Investment'}
          </h1>
        </div>
      </div>

      {/* Step Progress Bar */}
      <Card className="bg-white shadow-sm border">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="flex items-center w-full max-w-lg">
              {/* Step 1 */}
              <div className="flex flex-col items-center flex-1">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 transition-all duration-200 ${currentStep >= 1
                  ? 'bg-blue-600 border-blue-600 text-white shadow-lg'
                  : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                  {currentStep > 1 ? <CheckCircle className="w-5 h-5" /> : '1'}
                </div>
                <div className="mt-3 text-center">
                  <p className={`text-sm font-medium ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'
                    }`}>
                    Investment Details
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Client, scheme & amount</p>
                </div>
              </div>

              {/* Progress Line */}
              <div className="flex-1 mx-6">
                <div className={`h-0.5 rounded-full transition-all duration-300 ${currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-300'
                  }`}></div>
              </div>

              {/* Step 2 */}
              <div className="flex flex-col items-center flex-1">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 transition-all duration-200 ${currentStep >= 2
                  ? 'bg-blue-600 border-blue-600 text-white shadow-lg'
                  : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                  2
                </div>
                <div className="mt-3 text-center">
                  <p className={`text-sm font-medium ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'
                    }`}>
                    Transaction Details
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Payment & reference</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {isReinvestment && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <p className="text-blue-800 text-sm">
              This is a reinvestment from a matured investment. The amount is pre-filled from the maturity amount.
            </p>
          </CardContent>
        </Card>
      )}

      {currentStep === 1 ? renderInvestmentForm() : renderTransactionForm()}
    </div>
  );
};

export default InvestmentForm;
