
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Mail, Phone, Loader2, Shield, TrendingUp, Users, Lock } from 'lucide-react';

const Auth: React.FC = () => {
  const { user, signIn, signInWithPhone, resetPassword } = useAuth();
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('signin');
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [showPassword, setShowPassword] = useState(false);

  // Redirect if user is already authenticated
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (loginMethod === 'email') {
      await signIn(email, password);
    } else {
      await signInWithPhone(phone, password);
    }

    setLoading(false);
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    await resetPassword(email);
    setLoading(false);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50 to-blue-100 p-4">
      <div className="w-full max-w-lg">
        <Card className="shadow-xl border border-gray-200 bg-white rounded-xl">
          <CardHeader className="text-center space-y-6 pb-8">
            <div className="flex justify-center items-center gap-4 mb-6">
              <div className="p-3 bg-blue-100 rounded-full">
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </div>
            <div className="space-y-2">
              <CardTitle className="text-3xl font-bold text-gray-800">
                Investment Portal
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                Access your investment management system
              </CardDescription>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-100 p-1 rounded-lg">
                <TabsTrigger 
                  value="signin" 
                  className="rounded-md font-semibold transition-all data-[state=active]:bg-white data-[state=active]:shadow-sm text-gray-700 data-[state=active]:text-blue-600"
                >
                  Sign In
                </TabsTrigger>
                <TabsTrigger 
                  value="reset" 
                  className="rounded-md font-semibold transition-all data-[state=active]:bg-white data-[state=active]:shadow-sm text-gray-700 data-[state=active]:text-blue-600"
                >
                  Reset Password
                </TabsTrigger>
              </TabsList>

              <TabsContent value="signin" className="space-y-6">
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant={loginMethod === 'email' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setLoginMethod('email')}
                      className="flex-1 transition-all duration-200 hover:scale-105 bg-blue-500 hover:bg-blue-600 text-white border-blue-500 hover:border-blue-600"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Email
                    </Button>
                    <Button
                      type="button"
                      variant={loginMethod === 'phone' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setLoginMethod('phone')}
                      className="flex-1 transition-all duration-200 hover:scale-105 bg-blue-500 hover:bg-blue-600 text-white border-blue-500 hover:border-blue-600"
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Phone
                    </Button>
                  </div>
                </div>

                <form onSubmit={handleSignIn} className="space-y-5">
                  {loginMethod === 'email' ? (
                    <div className="space-y-2">
                      <Label htmlFor="signin-email" className="text-sm font-medium text-gray-700">
                        Email Address
                      </Label>
                      <Input
                        id="signin-email"
                        type="email"
                        value={email}
                        autoComplete="email"
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-all duration-200"
                        placeholder="Enter your email"
                      />
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label htmlFor="signin-phone" className="text-sm font-medium text-gray-700">
                        Phone Number
                      </Label>
                      <Input
                        id="signin-phone"
                        type="tel"
                        value={phone}
                        autoComplete="tel"
                        placeholder="+1234567890"
                        onChange={(e) => setPhone(e.target.value)}
                        required
                        className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-all duration-200"
                      />
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="signin-password" className="text-sm font-medium text-gray-700">
                      Password
                    </Label>
                    <div className="relative">
                      <Input
                        id="signin-password"
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        autoComplete="current-password"
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        className="h-11 pr-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-all duration-200"
                        placeholder="Enter your password"
                      />
                      <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full h-11 bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white font-semibold rounded-lg transition-transform transform hover:scale-105 shadow-md hover:shadow-lg" 
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing In...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="reset" className="space-y-6">
                <div className="text-center space-y-4 mb-6">
                  <Lock className="h-8 w-8 text-blue-500 mx-auto" />
                  <h3 className="text-xl font-bold text-gray-800">Reset Your Password</h3>
                  <p className="text-sm text-gray-600">
                    Enter your email address and we'll send you a link to reset your password.
                  </p>
                </div>
                
                <form onSubmit={handleResetPassword} className="space-y-5">
                  <div className="space-y-2">
                    <Label htmlFor="reset-email" className="text-sm font-medium text-gray-700">
                      Email Address
                    </Label>
                    <Input
                      id="reset-email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-all duration-200"
                      placeholder="Enter your email"
                    />
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full h-11 bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white font-semibold rounded-lg transition-transform transform hover:scale-105 shadow-md hover:shadow-lg" 
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Reset Link'
                    )}
                  </Button>
                </form>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
        
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            🔒 Secure • ✅ Trusted • 💼 Professional Investment Management
          </p>
        </div>
      </div>
    </div>
  );
};

export default Auth;
