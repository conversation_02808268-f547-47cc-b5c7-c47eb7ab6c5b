import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { ArrowLeft, Save, Plus, Trash2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

const clientEditSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().optional(),
  email: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(val);
  }, 'Please enter a valid email format (e.g., <EMAIL>)'),
  country_code: z.string().min(1, 'Country code is required'),
  mobile_number: z
    .string()
    .min(1, 'Mobile number is required')
    .regex(/^\d{10}$/, 'Mobile number must be exactly 10 digits'),
  country: z.string().optional(),
  address_line_1: z.string().optional().or(z.literal('')),
  address_line_2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pincode: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    return /^\d{6}$/.test(val);
  }, 'Pincode must be exactly 6 digits'),
  village: z.string().optional(),
  cif_id: z.string().min(1, 'CIF ID is required'),
  pan_card_number: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    return /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(val);
  }, 'PAN Card format should be ********** (5 letters, 4 numbers, 1 letter)'),
  aadhar_number: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    return /^\d{12}$/.test(val);
  }, 'Aadhar number must be exactly 12 digits'),
  contact_person2: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    return /^\d{10}$/.test(val);
  }, 'Secondary contact must be exactly 10 digits'),
});

const nomineeSchema = z.object({
  name: z.string().min(1, 'Nominee name is required'),
  relation: z.string().min(1, 'Relation is required'),
  birthdate: z.string().optional(),
  document: z.any().optional(),
});

type ClientEditData = z.infer<typeof clientEditSchema>;
type NomineeData = z.infer<typeof nomineeSchema>;

const ClientEdit: React.FC = () => {
  // Utility function to capitalize first letter of each word
  const capitalizeFirstLetter = (text: string): string => {
    if (!text) return text;
    return text
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [nominees, setNominees] = useState<NomineeData[]>([]);
  const [files, setFiles] = useState({
    profile_photo: null as File | null,
    aadhar_photo: null as File | null,
    pan_photo: null as File | null,
  });
  const [existingImages, setExistingImages] = useState({
    client_photo_url: '',
    aadhar_photo_url: '',
    pan_photo_url: '',
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [liveErrors, setLiveErrors] = useState<Record<string, string>>({});
  const [originalData, setOriginalData] = useState<ClientEditData | null>(null);

  const form = useForm<ClientEditData>({
    resolver: zodResolver(clientEditSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      country_code: '+91',
      mobile_number: '',
      country: 'India',
      address_line_1: '',
      address_line_2: '',
      city: '',
      state: '',
      pincode: '',
      village: '',
      cif_id: '',
      pan_card_number: '',
      aadhar_number: '',
      contact_person2: '',
    },
  });

  useEffect(() => {
    if (id) {
      fetchClientData();
    }
  }, [id]);

  const fetchClientData = async () => {
    try {
      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', id)
        .eq('is_deleted', false)
        .single();

      if (clientError) throw clientError;

      if (clientData) {
        // Split address back into two lines
        const addressLines = clientData.address?.split('\n') || [''];

        const formData = {
          first_name: clientData.first_name,
          last_name: clientData.last_name || '',
          email: clientData.email || '',
          country_code: clientData.country_code || '+91',
          mobile_number: clientData.mobile_number,
          country: clientData.country || 'India',
          address_line_1: addressLines[0] || '',
          address_line_2: addressLines[1] || '',
          city: clientData.city || '',
          state: clientData.state || '',
          pincode: clientData.pincode || '',
          village: clientData.village || '',
          cif_id: clientData.cif_id || '',
          pan_card_number: clientData.pan_card_number,
          aadhar_number: clientData.aadhar_number,
          contact_person2: clientData.contact_person2 || '',
        };

        form.reset(formData);
        setOriginalData(formData);

        setExistingImages({
          client_photo_url: clientData.client_photo_url || '',
          aadhar_photo_url: clientData.aadhar_photo_url || '',
          pan_photo_url: clientData.pan_photo_url || '',
        });
      }

      // Fetch nominees
      const { data: nomineeData, error: nomineeError } = await supabase
        .from('nominees')
        .select('*')
        .eq('client_id', id);

      if (nomineeError) throw nomineeError;

      if (nomineeData) {
        setNominees(nomineeData.map(nominee => ({
          name: nominee.name,
          relation: nominee.relation,
          birthdate: nominee.birthdate || '',
          document: null,
        })));
      }
    } catch (error) {
      console.error('Error fetching client:', error);
      toast({
        title: "Error",
        description: "Failed to fetch client data",
        variant: "destructive",
      });
      navigate('/clients');
    } finally {
      setFetchLoading(false);
    }
  };

  const validateField = (field: string, value: string) => {
    const newLiveErrors = { ...liveErrors };

    switch (field) {
      case 'email':
        if (value && value !== '') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            newLiveErrors.email = 'Please enter a valid email format (e.g., <EMAIL>)';
          } else {
            delete newLiveErrors.email;
          }
        } else {
          delete newLiveErrors.email;
        }
        break;

      case 'country_code':
        if (!value || value === '') {
          newLiveErrors.country_code = 'Country code is required';
        } else if (!/^\+\d{1,4}$/.test(value)) {
          newLiveErrors.country_code = 'Country code must start with + followed by 1-4 digits (e.g., +91)';
        } else {
          delete newLiveErrors.country_code;
        }
        break;

      case 'mobile_number':
        if (value.length > 0 && value.length < 10) {
          newLiveErrors.mobile_number = 'Mobile number must be exactly 10 digits';
        } else if (value.length === 10 && !/^\d{10}$/.test(value)) {
          newLiveErrors.mobile_number = 'Mobile number must contain only digits';
        } else {
          delete newLiveErrors.mobile_number;
        }
        break;

      case 'pan_card_number':
        if (value.length > 0 && value.length < 10) {
          newLiveErrors.pan_card_number = 'PAN Card must be exactly 10 characters (e.g., **********)';
        } else if (value.length === 10 && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
          newLiveErrors.pan_card_number = 'PAN Card format should be ********** (5 letters, 4 numbers, 1 letter)';
        } else {
          delete newLiveErrors.pan_card_number;
        }
        break;

      case 'aadhar_number':
        if (value.length > 0 && value.length < 12) {
          newLiveErrors.aadhar_number = 'Aadhar number must be exactly 12 digits';
        } else if (value.length === 12 && !/^\d{12}$/.test(value)) {
          newLiveErrors.aadhar_number = 'Aadhar number must contain only digits';
        } else {
          delete newLiveErrors.aadhar_number;
        }
        break;

      case 'contact_person2':
        if (value && value.length > 0 && value.length < 10) {
          newLiveErrors.contact_person2 = 'Secondary contact must be exactly 10 digits';
        } else if (value && value.length === 10 && !/^\d{10}$/.test(value)) {
          newLiveErrors.contact_person2 = 'Secondary contact must contain only digits';
        } else {
          delete newLiveErrors.contact_person2;
        }
        break;

      case 'pincode':
        if (value && value.length > 0 && value.length < 6) {
          newLiveErrors.pincode = 'Pincode must be exactly 6 digits';
        } else if (value && value.length === 6 && !/^\d{6}$/.test(value)) {
          newLiveErrors.pincode = 'Pincode must contain only digits';
        } else {
          delete newLiveErrors.pincode;
        }
        break;
    }

    setLiveErrors(newLiveErrors);
  };

  const checkDuplicate = async (field: string, value: string) => {
    if (!value || !originalData) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
      return;
    }

    // Skip duplicate check if value hasn't changed
    if (originalData[field as keyof ClientEditData] === value) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
      return;
    }

    const { data, error } = await supabase
      .from('clients')
      .select('id')
      .eq(field, value)
      .eq('is_deleted', false)
      .neq('id', id)
      .limit(1);

    if (error) return;

    if (data && data.length > 0) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: `This ${field.replace('_', ' ')} already exists`
      }));
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleFieldBlur = (field: string, value: string) => {
    if (['email', 'mobile_number', 'pan_card_number', 'aadhar_number'].includes(field)) {
      checkDuplicate(field, value);
    }
  };

  const addNominee = () => {
    setNominees([...nominees, { name: '', relation: '', birthdate: '', document: null }]);
  };

  const removeNominee = (index: number) => {
    setNominees(nominees.filter((_, i) => i !== index));
  };

  const updateNominee = (index: number, field: keyof NomineeData, value: string | File | null) => {
    const updated = [...nominees];
    updated[index] = { ...updated[index], [field]: value };
    setNominees(updated);
  };

  const handleFileChange = (type: keyof typeof files, file: File | null) => {
    setFiles({ ...files, [type]: file });
  };

  const uploadFile = async (file: File, path: string) => {
    const { data, error } = await supabase.storage
      .from('client-documents')
      .upload(path, file);

    if (error) throw error;
    return data.path;
  };

  const getImageUrl = (path: string) => {
    if (!path) return null;
    const { data } = supabase.storage.from('client-documents').getPublicUrl(path);
    return data.publicUrl;
  };

  const onSubmit = async (data: ClientEditData) => {
    // Check if there are any validation errors
    if (Object.keys(validationErrors).length > 0 || Object.keys(liveErrors).length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before submitting",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Combine address lines
      const fullAddress = data.address_line_2
        ? `${data.address_line_1}\n${data.address_line_2}`
        : data.address_line_1;

      // Upload new files if present
      let client_photo_url = existingImages.client_photo_url;
      let aadhar_photo_url = existingImages.aadhar_photo_url;
      let pan_photo_url = existingImages.pan_photo_url;

      if (files.profile_photo) {
        const path = `clients/${Date.now()}_profile.${files.profile_photo.name.split('.').pop()}`;
        client_photo_url = await uploadFile(files.profile_photo, path);
      }

      if (files.aadhar_photo) {
        const path = `clients/${Date.now()}_aadhar.${files.aadhar_photo.name.split('.').pop()}`;
        aadhar_photo_url = await uploadFile(files.aadhar_photo, path);
      }

      if (files.pan_photo) {
        const path = `clients/${Date.now()}_pan.${files.pan_photo.name.split('.').pop()}`;
        pan_photo_url = await uploadFile(files.pan_photo, path);
      }

      // Prepare client data
      const clientData = {
        first_name: data.first_name,
        last_name: data.last_name || null,
        email: data.email || null,
        country_code: data.country_code,
        mobile_number: data.mobile_number,
        country: data.country || null,
        address: fullAddress,
        city: data.city || null,
        state: data.state || null,
        pincode: data.pincode || null,
        village: data.village || null,
        cif_id: data.cif_id,
        pan_card_number: data.pan_card_number,
        aadhar_number: data.aadhar_number,
        contact_person2: data.contact_person2 || null,
        client_photo_url,
        aadhar_photo_url,
        pan_photo_url,
      };

      // Update client
      const { error } = await supabase
        .from('clients')
        .update(clientData)
        .eq('id', id);

      if (error) throw error;

      // Delete existing nominees and insert new ones
      await supabase.from('nominees').delete().eq('client_id', id);

      if (nominees.length > 0) {
        const nomineeData = await Promise.all(
          nominees
            .filter(nominee => nominee.name.trim() !== '')
            .map(async nominee => {
              let documentUrl = null;

              if (nominee.document) {
                const path = `nominees/${Date.now()}_${nominee.document.name}`;
                documentUrl = await uploadFile(nominee.document, path);
              }

              return {
                name: nominee.name,
                relation: nominee.relation,
                birthdate: nominee.birthdate || null,
                client_id: id,
                document_url: documentUrl,
              };
            })
        );

        if (nomineeData.length > 0) {
          const { error: nomineeError } = await supabase
            .from('nominees')
            .insert(nomineeData);

          if (nomineeError) throw nomineeError;
        }
      }

      toast({
        title: "Success",
        description: "Client updated successfully",
      });

      navigate(`/clients/${id}`);
    } catch (error) {
      console.error('Error updating client:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update client",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading client data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/clients/${id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Client</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Edit Client</h1>
        </div>
      </div>

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Personal Information and Account Information - Side by Side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information - Left Side - 4 rows */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter first name"
                          {...field}
                          onChange={(e) => {
                            const capitalized = capitalizeFirstLetter(e.target.value);
                            field.onChange(capitalized);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter last name (optional)"
                          {...field}
                          onChange={(e) => {
                            const capitalized = capitalizeFirstLetter(e.target.value);
                            field.onChange(capitalized);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter email (optional)"
                          {...field}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            validateField('email', e.target.value);
                          }}
                          onBlur={(e) => handleFieldBlur('email', e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                      {liveErrors.email && (
                        <p className="text-sm text-red-500">{liveErrors.email}</p>
                      )}
                      {validationErrors.email && (
                        <p className="text-sm text-red-500">{validationErrors.email}</p>
                      )}
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="country_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country Code *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="+91"
                            {...field}
                            maxLength={5}
                            onChange={(e) => {
                              let value = e.target.value;
                              // Ensure it starts with +
                              if (!value.startsWith('+') && value.length > 0) {
                                value = '+' + value.replace(/\D/g, '');
                              } else if (value.startsWith('+')) {
                                value = '+' + value.slice(1).replace(/\D/g, '');
                              }
                              field.onChange(value);
                              validateField('country_code', value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                        {liveErrors.country_code && (
                          <p className="text-sm text-red-500">{liveErrors.country_code}</p>
                        )}
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="mobile_number"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Mobile Number *</FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="Enter 10-digit mobile number"
                            {...field}
                            maxLength={10}
                            onChange={(e) => {
                              const value = e.target.value.replace(/\D/g, '');
                              field.onChange(value);
                              validateField('mobile_number', value);
                            }}
                            onBlur={(e) => handleFieldBlur('mobile_number', e.target.value)}
                          />
                        </FormControl>
                        <FormMessage />
                        {liveErrors.mobile_number && (
                          <p className="text-sm text-red-500">{liveErrors.mobile_number}</p>
                        )}
                        {validationErrors.mobile_number && (
                          <p className="text-sm text-red-500">{validationErrors.mobile_number}</p>
                        )}
                      </FormItem>
                    )}
                  />
                </div>

              </CardContent>
            </Card>

            {/* Account & Identity Information - Right Side - 4 rows */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Account Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="cif_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CIF ID *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter CIF ID" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="pan_card_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>PAN Card Number (e.g., **********)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter PAN card number (optional)"
                          {...field}
                          maxLength={10}
                          style={{ textTransform: 'uppercase' }}
                          onChange={(e) => {
                            const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
                            field.onChange(value);
                            validateField('pan_card_number', value);
                          }}
                          onBlur={(e) => handleFieldBlur('pan_card_number', e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                      {liveErrors.pan_card_number && (
                        <p className="text-sm text-red-500">{liveErrors.pan_card_number}</p>
                      )}
                      {validationErrors.pan_card_number && (
                        <p className="text-sm text-red-500">{validationErrors.pan_card_number}</p>
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="aadhar_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Aadhar Number (12 digits)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter 12-digit Aadhar number (optional)"
                          {...field}
                          maxLength={12}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, '');
                            field.onChange(value);
                            validateField('aadhar_number', value);
                          }}
                          onBlur={(e) => handleFieldBlur('aadhar_number', e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                      {liveErrors.aadhar_number && (
                        <p className="text-sm text-red-500">{liveErrors.aadhar_number}</p>
                      )}
                      {validationErrors.aadhar_number && (
                        <p className="text-sm text-red-500">{validationErrors.aadhar_number}</p>
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contact_person2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Secondary Contact Person (Mobile Number)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter 10-digit mobile number (optional)"
                          {...field}
                          maxLength={10}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, '');
                            field.onChange(value);
                            validateField('contact_person2', value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                      {liveErrors.contact_person2 && (
                        <p className="text-sm text-red-500">{liveErrors.contact_person2}</p>
                      )}
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Address Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="address_line_1"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address Line 1</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter address line 1 (optional)"
                        {...field}
                        onChange={(e) => {
                          const capitalized = capitalizeFirstLetter(e.target.value);
                          field.onChange(capitalized);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address_line_2"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address Line 2</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter address line 2 (optional)"
                        {...field}
                        onChange={(e) => {
                          const capitalized = capitalizeFirstLetter(e.target.value);
                          field.onChange(capitalized);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter country (optional)"
                          {...field}
                          onChange={(e) => {
                            const capitalized = capitalizeFirstLetter(e.target.value);
                            field.onChange(capitalized);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter state (optional)"
                          {...field}
                          onChange={(e) => {
                            const capitalized = capitalizeFirstLetter(e.target.value);
                            field.onChange(capitalized);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter city (optional)"
                          {...field}
                          onChange={(e) => {
                            const capitalized = capitalizeFirstLetter(e.target.value);
                            field.onChange(capitalized);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="village"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Village</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter village (optional)"
                          {...field}
                          onChange={(e) => {
                            const capitalized = capitalizeFirstLetter(e.target.value);
                            field.onChange(capitalized);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="pincode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pincode</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter 6-digit pincode (optional)"
                          {...field}
                          maxLength={6}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, '');
                            field.onChange(value);
                            validateField('pincode', value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                      {liveErrors.pincode && (
                        <p className="text-sm text-red-500">{liveErrors.pincode}</p>
                      )}
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>



          {/* Document Uploads */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Document Uploads (Optional)</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Profile Photo</label>
                  {existingImages.client_photo_url && (
                    <div className="mb-2">
                      <img
                        src={getImageUrl(existingImages.client_photo_url)}
                        alt="Current Profile"
                        className="w-20 h-20 object-cover rounded"
                      />
                      <p className="text-xs text-gray-500">Current photo</p>
                    </div>
                  )}
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange('profile_photo', e.target.files?.[0] || null)}
                    className="cursor-pointer"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Aadhar Card</label>
                  {existingImages.aadhar_photo_url && (
                    <div className="mb-2">
                      <img
                        src={getImageUrl(existingImages.aadhar_photo_url)}
                        alt="Current Aadhar"
                        className="w-20 h-20 object-cover rounded"
                      />
                      <p className="text-xs text-gray-500">Current photo</p>
                    </div>
                  )}
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange('aadhar_photo', e.target.files?.[0] || null)}
                    className="cursor-pointer"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">PAN Card</label>
                  {existingImages.pan_photo_url && (
                    <div className="mb-2">
                      <img
                        src={getImageUrl(existingImages.pan_photo_url)}
                        alt="Current PAN"
                        className="w-20 h-20 object-cover rounded"
                      />
                      <p className="text-xs text-gray-500">Current photo</p>
                    </div>
                  )}
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange('pan_photo', e.target.files?.[0] || null)}
                    className="cursor-pointer"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Nominees Section with Document Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl flex items-center justify-between">
                Nominees (Optional)
                <Button type="button" variant="outline" size="sm" onClick={addNominee}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Nominee
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {nominees.map((nominee, index) => (
                <div key={index} className="border p-4 rounded-lg">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="font-medium">Nominee {index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeNominee(index)}
                      className="text-red-500"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Name *</label>
                      <Input
                        placeholder="Enter nominee name"
                        value={nominee.name}
                        onChange={(e) => updateNominee(index, 'name', capitalizeFirstLetter(e.target.value))}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Relation *</label>
                      <Input
                        placeholder="Enter relation"
                        value={nominee.relation}
                        onChange={(e) => updateNominee(index, 'relation', capitalizeFirstLetter(e.target.value))}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Birthdate</label>
                      <Input
                        type="date"
                        value={nominee.birthdate}
                        onChange={(e) => updateNominee(index, 'birthdate', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Document (Optional)</label>
                      <Input
                        type="file"
                        accept="image/*,.pdf,.doc,.docx"
                        onChange={(e) => updateNominee(index, 'document', e.target.files?.[0] || null)}
                        className="cursor-pointer"
                      />
                    </div>
                  </div>
                </div>
              ))}
              {nominees.length === 0 && (
                <p className="text-gray-500 text-center py-4">No nominees added</p>
              )}
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/clients/${id}`)}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="w-full sm:w-auto"
            >
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Updating...' : 'Update Client'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ClientEdit;
