import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, ArrowRight, CheckCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { formatDisplayDate } from '@/utils/dateFormat';
import ClientSelector from '@/components/investment/ClientSelector';
import SchemeSelector from '@/components/investment/SchemeSelector';
import NomineeSelector from '@/components/investment/NomineeSelector';
import ClientDetailsCard from '@/components/investment/ClientDetailsCard';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  village: string | null;
  cif_id: string | null;
}

interface Scheme {
  id: string;
  name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  min_amount: number;
  max_amount: number;
  lock_in_period_months: number;
  commission_percentage: number;
  compounding_frequency: string;
  payout_type: string;
  supports_sip: boolean;
}

interface Nominee {
  id: string;
  name: string;
  relation: string;
}

interface SbAccount {
  id: string;
  sb_account_number: string;
  account_type: string;
  status: string;
  client_sb_accounts: {
    role: string;
    client_id: string;
    clients: {
      id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

const InvestmentEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [tdsPercentage, setTdsPercentage] = useState(10);

  // Form state
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedSecondaryApplicant, setSelectedSecondaryApplicant] = useState<Client | null>(null);
  const [selectedScheme, setSelectedScheme] = useState<Scheme | null>(null);
  const [selectedNominee, setSelectedNominee] = useState<Nominee | null>(null);
  const [amount, setAmount] = useState<string>('');
  const [investmentDate, setInvestmentDate] = useState<Date>();
  const [maturityDate, setMaturityDate] = useState<Date>();
  const [maturityAmount, setMaturityAmount] = useState<number>(0);
  const [commission, setCommission] = useState<number>(0);
  const [tdsAmount, setTdsAmount] = useState<number>(0);
  const [actualProfit, setActualProfit] = useState<number>(0);
  const [remark, setRemark] = useState('');
  const [liveErrors, setLiveErrors] = useState<Record<string, string>>({});
  const [primaryCifId, setPrimaryCifId] = useState<string>('');
  const [secondaryCifId, setSecondaryCifId] = useState<string>('');

  // Transaction state
  const [paymentMode, setPaymentMode] = useState('');
  const [referenceNumber, setReferenceNumber] = useState('');
  const [originalAmount, setOriginalAmount] = useState<number>(0);
  const [sbAccounts, setSbAccounts] = useState<SbAccount[]>([]);
  const [selectedSbAccount, setSelectedSbAccount] = useState('');
  const [showNewAccountForm, setShowNewAccountForm] = useState(false);
  const [newAccountNumber, setNewAccountNumber] = useState('');
  const [transactionErrors, setTransactionErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (id) {
      fetchInvestmentData();
    }
    fetchTdsPercentage();
  }, [id]);

  useEffect(() => {
    if (selectedScheme && amount && investmentDate) {
      calculateMaturityDetails();
    }
  }, [selectedScheme, amount, investmentDate, tdsPercentage]);

  // Update CIF IDs when clients change (only if not already set)
  useEffect(() => {
    if (selectedClient && !primaryCifId) {
      setPrimaryCifId(selectedClient.cif_id || '');
    }
  }, [selectedClient, primaryCifId]);

  useEffect(() => {
    if (selectedSecondaryApplicant && !secondaryCifId) {
      setSecondaryCifId(selectedSecondaryApplicant.cif_id || '');
    } else if (!selectedSecondaryApplicant) {
      setSecondaryCifId('');
    }
  }, [selectedSecondaryApplicant, secondaryCifId]);

  const fetchTdsPercentage = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_settings')
        .select('tds_percentage')
        .maybeSingle();

      if (error) {
        console.error('Error fetching TDS percentage:', error);
        return;
      }
      if (data?.tds_percentage) {
        setTdsPercentage(data.tds_percentage);
      }
    } catch (error) {
      console.error('Error fetching TDS percentage:', error);
    }
  };

  const calculateMaturityDetails = () => {
    if (!selectedScheme || !amount || !investmentDate) return;

    const principal = parseFloat(amount);
    const rate = selectedScheme.interest_rate;
    const tenureMonths = selectedScheme.tenure_months;

    // Maturity Date
    const maturity = new Date(investmentDate);
    maturity.setMonth(maturity.getMonth() + tenureMonths);
    setMaturityDate(maturity);

    // Monthly interest rate
    const monthlyRate = rate / 100 / 12;

    // Maturity Amount
    const maturityAmt = principal + (principal * monthlyRate * tenureMonths);
    setMaturityAmount(maturityAmt);

    // Commission
    const commissionAmt = (principal * selectedScheme.commission_percentage) / 100;
    setCommission(commissionAmt);

    // TDS on commission
    const tds = (commissionAmt * tdsPercentage) / 100;
    setTdsAmount(tds);

    // Actual Profit = Commission - TDS
    const actualProfitAmt = commissionAmt - tds;
    setActualProfit(actualProfitAmt);
  };

  const fetchSbAccounts = async () => {
    if (!selectedClient) {
      setSbAccounts([]);
      return;
    }
    await fetchSbAccountsForClients(selectedClient, selectedSecondaryApplicant);
  };

  const fetchSbAccountsForClients = async (primaryClient: Client, secondaryClient?: Client | null) => {
    try {
      const clientIds = [primaryClient.id];
      if (secondaryClient) {
        clientIds.push(secondaryClient.id);
      }

      const { data, error } = await supabase
        .from('sb_accounts')
        .select(`
          id,
          sb_account_number,
          account_type,
          status,
          client_sb_accounts (
            role,
            client_id,
            clients (
              id,
              first_name,
              last_name
            )
          )
        `)
        .eq('is_deleted', false)
        .eq('status', 'active');

      if (error) throw error;

      const filteredAccounts = (data || []).filter(account => {
        return account.client_sb_accounts.some(csa =>
          clientIds.includes(csa.client_id)
        );
      });

      setSbAccounts(filteredAccounts);
    } catch (error) {
      console.error('Error fetching SB accounts:', error);
    }
  };

  const fetchInvestmentData = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          client:clients!client_id (id, first_name, last_name, email, mobile_number, village, cif_id),
          secondary_applicant:clients!second_applicant_id (id, first_name, last_name, email, mobile_number, village, cif_id),
          nominee:nominees!nominee_id (id, name, relation)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // Set form values
      setSelectedClient(data.client);
      setSelectedSecondaryApplicant(data.secondary_applicant);
      setSelectedNominee(data.nominee);
      setAmount(data.amount.toString());
      setInvestmentDate(new Date(data.investment_date));
      setRemark(data.remark || '');
      setOriginalAmount(data.amount);

      // Set CIF IDs
      setPrimaryCifId(data.primary_applicant_cif_id || data.client?.cif_id || '');
      setSecondaryCifId(data.secondary_applicant_cif_id || data.secondary_applicant?.cif_id || '');

      // Fetch transaction data
      const { data: transactionData } = await supabase
        .from('transactions')
        .select('payment_mode, reference_number, sb_account_id')
        .eq('investment_id', id)
        .eq('amount_type', 'investment')
        .single();

      if (transactionData) {
        setPaymentMode(transactionData.payment_mode || '');
        setReferenceNumber(transactionData.reference_number || '');
        if (transactionData.sb_account_id) {
          setSelectedSbAccount(transactionData.sb_account_id);
        }
      }

      // Fetch SB accounts after setting clients
      if (data.client) {
        await fetchSbAccountsForClients(data.client, data.secondary_applicant);
      }

      // Create scheme object from stored data
      const scheme: Scheme = {
        id: data.scheme_id,
        name: data.scheme_name,
        scheme_code: data.scheme_code,
        interest_rate: data.interest_rate,
        interest_type: data.interest_type,
        tenure_months: data.tenure_months,
        min_amount: data.min_amount,
        max_amount: data.max_amount,
        lock_in_period_months: data.lock_in_period_months,
        commission_percentage: data.commission_percentage,
        compounding_frequency: data.compounding_frequency,
        payout_type: data.payout_status,
        supports_sip: data.supports_sip
      };
      setSelectedScheme(scheme);

    } catch (error) {
      console.error('Error fetching investment:', error);
      toast({
        title: "Error",
        description: "Failed to fetch investment details",
        variant: "destructive",
      });
    } finally {
      setInitialLoading(false);
    }
  };

  // Format dates to avoid timezone issues
  const formatDateForDB = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const validateInvestmentFields = () => {
    const newLiveErrors: Record<string, string> = {};

    if (!selectedClient) {
      newLiveErrors.client = 'Primary client is required';
    }
    if (!selectedScheme) {
      newLiveErrors.scheme = 'Investment scheme is required';
    }
    if (!amount || amount.trim() === '') {
      newLiveErrors.amount = 'Investment amount is required';
    } else {
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        newLiveErrors.amount = 'Investment amount must be a valid number';
      } else if (numericAmount < 0) {
        newLiveErrors.amount = 'Investment amount cannot be negative';
      } else if (numericAmount === 0) {
        newLiveErrors.amount = 'Investment amount must be greater than zero';
      } else if (selectedScheme) {
        if (numericAmount < selectedScheme.min_amount) {
          newLiveErrors.amount = `Amount must be at least ₹${selectedScheme.min_amount.toLocaleString()}`;
        } else if (selectedScheme.max_amount && numericAmount > selectedScheme.max_amount) {
          newLiveErrors.amount = `Amount cannot exceed ₹${selectedScheme.max_amount.toLocaleString()}`;
        }
      }
    }
    if (!investmentDate) {
      newLiveErrors.investment_date = 'Investment date is required';
    }

    setLiveErrors(newLiveErrors);
    return Object.keys(newLiveErrors).length === 0;
  };

  const handleNextStep = () => {
    const isValid = validateInvestmentFields();
    if (!isValid) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before proceeding",
        variant: "destructive",
      });
      return;
    }
    setCurrentStep(2);
  };

  const validateTransactionFields = () => {
    const newTransactionErrors: Record<string, string> = {};

    if (!paymentMode) {
      newTransactionErrors.payment_mode = 'Payment mode is required';
    }
    // Reference number is required for cheque and ecs, optional for cash and rtgs/neft
    if ((paymentMode === 'cheque' || paymentMode === 'ecs') && (!referenceNumber || referenceNumber.trim() === '')) {
      newTransactionErrors.reference_number = 'Reference number is required for cheque and ECS payments';
    }

    setTransactionErrors(newTransactionErrors);
    return Object.keys(newTransactionErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isTransactionValid = validateTransactionFields();
    if (!isTransactionValid) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before submitting",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const updateData = {
        client_id: selectedClient.id,
        second_applicant_id: selectedSecondaryApplicant?.id || null,
        scheme_id: selectedScheme.id,
        nominee_id: selectedNominee?.id || null,
        amount: parseFloat(amount),
        investment_date: formatDateForDB(investmentDate),
        start_date: null,
        maturity_date: maturityDate ? formatDateForDB(maturityDate) : null,
        maturity_amount: maturityAmount,
        remark: remark || null,
        actual_profit: actualProfit,
        tds_amount: tdsAmount,
        // Update CIF IDs
        primary_applicant_cif_id: primaryCifId || null,
        secondary_applicant_cif_id: secondaryCifId || null,
        // Update scheme details
        scheme_name: selectedScheme.name,
        scheme_code: selectedScheme.scheme_code,
        interest_rate: selectedScheme.interest_rate,
        interest_type: selectedScheme.interest_type,
        tenure_months: selectedScheme.tenure_months,
        min_amount: selectedScheme.min_amount,
        max_amount: selectedScheme.max_amount,
        lock_in_period_months: selectedScheme.lock_in_period_months,
        commission_percentage: selectedScheme.commission_percentage,
        compounding_frequency: selectedScheme.compounding_frequency,
        payout_status: selectedScheme.payout_type,
        supports_sip: selectedScheme.supports_sip,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('investments')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;

      // Update transaction
      const transactionUpdateData: Record<string, unknown> = {
        payment_mode: paymentMode,
        reference_number: paymentMode === 'cash' ? null : (referenceNumber || null),
        updated_at: new Date().toISOString(),
        sb_account_id: null // No SB account for investment forms
      };

      // Add amount if it changed
      if (parseFloat(amount) !== originalAmount) {
        transactionUpdateData.amount = parseFloat(amount);
      }

      const { error: transactionError } = await supabase
        .from('transactions')
        .update(transactionUpdateData)
        .eq('investment_id', id)
        .eq('amount_type', 'investment');

      if (transactionError) {
        console.error('Error updating transaction:', transactionError);
      }

      toast({
        title: "Success",
        description: "Investment and transaction updated successfully",
      });

      navigate(`/investments/${id}`);
    } catch (error) {
      console.error('Error updating investment:', error);
      toast({
        title: "Error",
        description: "Failed to update investment",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading investment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2 sm:gap-4">
        <Button variant="ghost" size="sm" onClick={() => navigate(`/investments/${id}`)} className="p-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1 min-w-0">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold truncate">Edit Investment</h1>
        </div>
      </div>

      {/* Step Progress Bar */}
      <Card className="bg-white shadow-sm border">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="flex items-center w-full max-w-lg">
              {/* Step 1 */}
              <div className="flex flex-col items-center flex-1">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 transition-all duration-200 ${currentStep >= 1
                  ? 'bg-blue-600 border-blue-600 text-white shadow-lg'
                  : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                  {currentStep > 1 ? <CheckCircle className="w-5 h-5" /> : '1'}
                </div>
                <div className="mt-3 text-center">
                  <p className={`text-sm font-medium ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-400'
                    }`}>
                    Investment Details
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Client, scheme & amount</p>
                </div>
              </div>

              {/* Progress Line */}
              <div className="flex-1 mx-6">
                <div className={`h-0.5 rounded-full transition-all duration-300 ${currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-300'
                  }`}></div>
              </div>

              {/* Step 2 */}
              <div className="flex flex-col items-center flex-1">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold border-2 transition-all duration-200 ${currentStep >= 2
                  ? 'bg-blue-600 border-blue-600 text-white shadow-lg'
                  : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                  2
                </div>
                <div className="mt-3 text-center">
                  <p className={`text-sm font-medium ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-400'
                    }`}>
                    Transaction Details
                  </p>
                  <p className="text-xs text-gray-500 mt-1">Payment & reference</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {currentStep === 1 ? (
        <form onSubmit={(e) => { e.preventDefault(); handleNextStep(); }}>
          <Card>
            <CardHeader>
              <CardTitle>Investment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
              {/* Client Selection Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Primary Client *</Label>
                  <ClientSelector
                    selectedClient={selectedClient}
                    onClientSelect={(client) => {
                      setSelectedClient(client);
                      if (client) {
                        setLiveErrors(prev => {
                          const newErrors = { ...prev };
                          delete newErrors.client;
                          return newErrors;
                        });
                      }
                    }}
                    required
                  />
                  {liveErrors.client && (
                    <p className="text-sm text-red-500 mt-1">{liveErrors.client}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label>Secondary Applicant (Optional)</Label>
                  <ClientSelector
                    selectedClient={selectedSecondaryApplicant}
                    onClientSelect={setSelectedSecondaryApplicant}
                    required={false}
                  />
                </div>
              </div>

              {/* Client Details Cards */}
              {(selectedClient || selectedSecondaryApplicant) && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {selectedClient && (
                    <ClientDetailsCard
                      client={selectedClient}
                      title="Primary Applicant Details"
                      cifId={primaryCifId}
                      onCifIdUpdate={setPrimaryCifId}
                      isEditable={true}
                    />
                  )}

                  {selectedSecondaryApplicant && (
                    <ClientDetailsCard
                      client={selectedSecondaryApplicant}
                      title="Secondary Applicant Details"
                      cifId={secondaryCifId}
                      onCifIdUpdate={setSecondaryCifId}
                      isEditable={true}
                    />
                  )}
                </div>
              )}

              {/* Scheme Selection */}
              <div className="space-y-2">
                <Label>Investment Scheme *</Label>
                <SchemeSelector
                  selectedScheme={selectedScheme}
                  onSchemeSelect={(scheme) => {
                    setSelectedScheme(scheme);
                    if (scheme) {
                      setLiveErrors(prev => {
                        const newErrors = { ...prev };
                        delete newErrors.scheme;
                        return newErrors;
                      });
                    }
                  }}
                />
                {liveErrors.scheme && (
                  <p className="text-sm text-red-500 mt-1">{liveErrors.scheme}</p>
                )}
              </div>

              {/* Show scheme details if selected */}
              {selectedScheme && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-3">Scheme Details</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div><span className="font-medium text-gray-700">Interest Rate:</span> <span className="text-gray-600">{selectedScheme.interest_rate}%</span></div>
                    <div><span className="font-medium text-gray-700">Tenure:</span> <span className="text-gray-600">{selectedScheme.tenure_months} months</span></div>
                    <div><span className="font-medium text-gray-700">Interest Type:</span> <span className="text-gray-600">{selectedScheme.interest_type}</span></div>
                    <div><span className="font-medium text-gray-700">Min Amount:</span> <span className="text-gray-600">₹{selectedScheme.min_amount?.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Max Amount:</span> <span className="text-gray-600">₹{selectedScheme.max_amount?.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Commission:</span> <span className="text-gray-600">{selectedScheme.commission_percentage}%</span></div>
                  </div>
                </div>
              )}

              {/* Amount and Investment Type Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">Investment Amount *</Label>
                  <Input
                    id="amount"
                    type="number"
                    onWheel={(e) => e.currentTarget.blur()}
                    placeholder="Enter investment amount"
                    value={amount}
                    onChange={(e) => {
                      const input = e.target.value;
                      setAmount(input);

                      // Live validation for amount
                      const newLiveErrors = { ...liveErrors };
                      if (!input || input.trim() === '') {
                        newLiveErrors.amount = 'Investment amount is required';
                      } else {
                        const numericAmount = parseFloat(input);
                        if (isNaN(numericAmount)) {
                          newLiveErrors.amount = 'Investment amount must be a valid number';
                        } else if (numericAmount < 0) {
                          newLiveErrors.amount = 'Investment amount cannot be negative';
                        } else if (numericAmount === 0) {
                          newLiveErrors.amount = 'Investment amount must be greater than zero';
                        } else if (selectedScheme) {
                          if (numericAmount < selectedScheme.min_amount) {
                            newLiveErrors.amount = `Amount must be at least ₹${selectedScheme.min_amount.toLocaleString()}`;
                          } else if (selectedScheme.max_amount && numericAmount > selectedScheme.max_amount) {
                            newLiveErrors.amount = `Amount cannot exceed ₹${selectedScheme.max_amount.toLocaleString()}`;
                          } else {
                            delete newLiveErrors.amount;
                          }
                        } else {
                          delete newLiveErrors.amount;
                        }
                      }
                      setLiveErrors(newLiveErrors);
                    }}
                    required
                  />
                  {liveErrors.amount && (
                    <p className="text-sm text-red-500 mt-1">{liveErrors.amount}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label>Investment Type</Label>
                  <Input value="Lumpsum" disabled />
                </div>
              </div>

              {/* Investment Date Field */}
              <div className="space-y-2">
                <Label>Investment Date *</Label>
                <DatePicker
                  date={investmentDate}
                  onSelect={(date) => {
                    setInvestmentDate(date);
                    if (date) {
                      setLiveErrors(prev => {
                        const newErrors = { ...prev };
                        delete newErrors.investment_date;
                        return newErrors;
                      });
                    }
                  }}
                  placeholder="Select investment date"
                />
                {liveErrors.investment_date && (
                  <p className="text-sm text-red-500 mt-1">{liveErrors.investment_date}</p>
                )}
              </div>

              {/* Nominee Selection */}
              {selectedClient && (
                <div className="space-y-2">
                  <Label>Nominee (Optional)</Label>
                  <NomineeSelector
                    clientId={selectedClient.id}
                    selectedNominee={selectedNominee}
                    onNomineeSelect={setSelectedNominee}
                  />
                </div>
              )}

              {/* Calculated Fields */}
              {maturityDate && (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-800 mb-3">Calculated Details</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div><span className="font-medium text-gray-700">Maturity Date:</span> <span className="text-gray-600">{formatDisplayDate(maturityDate)}</span></div>
                    <div><span className="font-medium text-gray-700">Maturity Amount:</span> <span className="text-gray-600">₹{maturityAmount.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Commission:</span> <span className="text-gray-600">₹{commission.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">TDS Amount:</span> <span className="text-gray-600">₹{tdsAmount.toLocaleString()}</span></div>
                    <div><span className="font-medium text-gray-700">Actual Profit:</span> <span className="text-gray-600">₹{actualProfit.toLocaleString()}</span></div>
                  </div>
                </div>
              )}

              {/* Remark */}
              <div className="space-y-2">
                <Label htmlFor="remark">Remark (Optional)</Label>
                <Textarea
                  id="remark"
                  placeholder="Enter any remarks"
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-end gap-3 sm:gap-4 pt-2">
                <Button type="button" variant="outline" onClick={() => navigate(`/investments/${id}`)} className="w-full sm:w-auto order-2 sm:order-1">
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={Object.keys(liveErrors).length > 0}
                  className="w-full sm:w-auto order-1 sm:order-2"
                >
                  <ArrowRight className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Next: Transaction Details</span>
                  <span className="sm:hidden">Next Step</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      ) : (
        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Transaction Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
              {/* Investment Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Investment Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div><strong>Client:</strong> {selectedClient?.first_name} {selectedClient?.last_name}</div>
                  <div><strong>Scheme:</strong> {selectedScheme?.name}</div>
                  <div><strong>Amount:</strong> ₹{parseFloat(amount).toLocaleString()}</div>
                  <div><strong>Type:</strong> Investment</div>
                </div>
              </div>

              {/* Payment Mode */}
              <div className="space-y-2">
                <Label>Payment Mode *</Label>
                <Select value={paymentMode} onValueChange={(value) => {
                  setPaymentMode(value);
                  if (value) {
                    setTransactionErrors(prev => {
                      const newErrors = { ...prev };
                      delete newErrors.payment_mode;
                      return newErrors;
                    });
                  }
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="rtgs/neft">RTGS/NEFT</SelectItem>
                    <SelectItem value="ecs">ECS</SelectItem>
                  </SelectContent>
                </Select>
                {transactionErrors.payment_mode && (
                  <p className="text-sm text-red-500 mt-1">{transactionErrors.payment_mode}</p>
                )}
              </div>

              {/* Reference Number */}
              <div className="space-y-2">
                <Label>
                  Reference Number / Cheque Number
                  {(paymentMode === 'cheque' || paymentMode === 'ecs') && ' *'}
                  {paymentMode === 'cash' && ' (Not Required)'}
                  {(paymentMode === 'rtgs/neft') && ' (Optional)'}
                </Label>
                <Input
                  placeholder={
                    paymentMode === 'cash'
                      ? "Reference number not required for cash"
                      : paymentMode === 'cheque'
                        ? "Enter cheque number"
                        : paymentMode === 'ecs'
                          ? "Enter ECS reference number"
                          : "Enter reference number"
                  }
                  value={referenceNumber}
                  onChange={(e) => {
                    const value = e.target.value;
                    setReferenceNumber(value);
                    if (value.trim()) {
                      setTransactionErrors(prev => {
                        const newErrors = { ...prev };
                        delete newErrors.reference_number;
                        return newErrors;
                      });
                    }
                  }}
                  disabled={paymentMode === 'cash'}
                />
                {transactionErrors.reference_number && (
                  <p className="text-sm text-red-500 mt-1">{transactionErrors.reference_number}</p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-3 sm:gap-4 pt-2">
                <Button type="button" variant="outline" onClick={() => setCurrentStep(1)} className="w-full sm:w-auto order-2 sm:order-1">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Back to Investment</span>
                  <span className="sm:hidden">Back</span>
                </Button>
                <Button
                  type="submit"
                  disabled={loading || Object.keys(transactionErrors).length > 0}
                  className="w-full sm:w-auto order-1 sm:order-2"
                >
                  {loading ? 'Updating...' : (
                    <>
                      <span className="hidden sm:inline">Update Investment</span>
                      <span className="sm:hidden">Update</span>
                    </>
                  )}
                </Button>
              </div>
            </CardContent >
          </Card >
        </form >
      )}
    </div >
  );
};

export default InvestmentEdit;
