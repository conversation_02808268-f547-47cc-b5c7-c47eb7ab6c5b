import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfQuarter, endOfQuarter, startOfYear, endOfYear, subDays, subWeeks, subMonths, subQuarters, subYears } from 'date-fns';

export interface DateRange {
  from: string;
  to: string;
}

interface EnhancedDateFilterProps {
  value: string;
  onChange: (value: string) => void;
  customRange?: DateRange;
  onCustomRangeChange?: (range: DateRange) => void;
}

const getDateRangeLabel = (key: string): string => {
  const today = new Date();
  const formatDate = (date: Date) => format(date, 'dd-MM-yy');

  switch (key) {
    case 'today':
      return `Today (${formatDate(today)})`;
    case 'yesterday':
      return `Yesterday (${formatDate(subDays(today, 1))})`;
    case 'this-week':
      return `This Week (${formatDate(startOfWeek(today, { weekStartsOn: 1 }))} to ${formatDate(endOfWeek(today, { weekStartsOn: 1 }))})`;
    case 'last-week': {
      const lastWeekStart = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });
      const lastWeekEnd = endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });
      return `Last Week (${formatDate(lastWeekStart)} to ${formatDate(lastWeekEnd)})`;
    }
    case 'last-7-days':
      return `Last 7 Days (${formatDate(subDays(today, 7))} to ${formatDate(today)})`;
    case 'this-month':
      return `This Month (${formatDate(startOfMonth(today))} to ${formatDate(endOfMonth(today))})`;
    case 'last-month': {
      const lastMonth = subMonths(today, 1);
      return `Last Month (${formatDate(startOfMonth(lastMonth))} to ${formatDate(endOfMonth(lastMonth))})`;
    }
    case 'last-30-days':
      return `Last 30 Days (${formatDate(subDays(today, 30))} to ${formatDate(today)})`;
    case 'this-quarter':
      return `This Quarter (${formatDate(startOfQuarter(today))} to ${formatDate(endOfQuarter(today))})`;
    case 'last-quarter': {
      const lastQuarter = subQuarters(today, 1);
      return `Last Quarter (${formatDate(startOfQuarter(lastQuarter))} to ${formatDate(endOfQuarter(lastQuarter))})`;
    }
    case 'last-90-days':
      return `Last 90 Days (${formatDate(subDays(today, 90))} to ${formatDate(today)})`;
    case 'last-180-days':
      return `Last 180 Days (${formatDate(subDays(today, 180))} to ${formatDate(today)})`;
    case 'this-year':
      return `This Year (${formatDate(startOfYear(today))} to ${formatDate(endOfYear(today))})`;
    case 'last-year': {
      const lastYear = subYears(today, 1);
      return `Last Year (${formatDate(startOfYear(lastYear))} to ${formatDate(endOfYear(lastYear))})`;
    }
    default:
      return 'All Time';
  }
};

export const EnhancedDateFilter: React.FC<EnhancedDateFilterProps> = ({
  value,
  onChange,
  customRange,
  onCustomRangeChange
}) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:gap-4 gap-2 w-full">
      <div className="w-full sm:w-64">
        <Select value={value} onValueChange={onChange}>
          <SelectTrigger className="w-full h-10 px-3">
            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
            <SelectValue placeholder="Select range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Time</SelectItem>
            <SelectItem value="today">{getDateRangeLabel('today')}</SelectItem>
            <SelectItem value="yesterday">{getDateRangeLabel('yesterday')}</SelectItem>
            <SelectItem value="this-week">{getDateRangeLabel('this-week')}</SelectItem>
            <SelectItem value="last-week">{getDateRangeLabel('last-week')}</SelectItem>
            <SelectItem value="last-7-days">{getDateRangeLabel('last-7-days')}</SelectItem>
            <SelectItem value="this-month">{getDateRangeLabel('this-month')}</SelectItem>
            <SelectItem value="last-month">{getDateRangeLabel('last-month')}</SelectItem>
            <SelectItem value="last-30-days">{getDateRangeLabel('last-30-days')}</SelectItem>
            <SelectItem value="this-quarter">{getDateRangeLabel('this-quarter')}</SelectItem>
            <SelectItem value="last-quarter">{getDateRangeLabel('last-quarter')}</SelectItem>
            <SelectItem value="last-90-days">{getDateRangeLabel('last-90-days')}</SelectItem>
            <SelectItem value="last-180-days">{getDateRangeLabel('last-180-days')}</SelectItem>
            <SelectItem value="this-year">{getDateRangeLabel('this-year')}</SelectItem>
            <SelectItem value="last-year">{getDateRangeLabel('last-year')}</SelectItem>
            <SelectItem value="custom">Custom Range</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {value === 'custom' && (
        <div className="flex flex-col sm:flex-row gap-2 w-full">
          <Input
            type="date"
            value={customRange?.from || ''}
            onChange={(e) =>
              onCustomRangeChange?.({ ...customRange!, from: e.target.value })
            }
            className="h-10 px-3 w-full sm:w-36"
          />
          <Input
            type="date"
            value={customRange?.to || ''}
            onChange={(e) =>
              onCustomRangeChange?.({ ...customRange!, to: e.target.value })
            }
            className="h-10 px-3 w-full sm:w-36"
          />
        </div>
      )}
    </div>

  );
};

export const getDateRangeValues = (
  filter: string,
  customRange?: DateRange,
  useFullDay: boolean = true
): { from: string; to: string } | null => {
  const today = new Date();
  const formatForDB = (date: Date, isStart: boolean) =>
    useFullDay
      ? isStart
        ? new Date(date.setHours(0, 0, 0, 0)).toISOString()
        : new Date(date.setHours(23, 59, 59, 999)).toISOString()
      : format(date, 'yyyy-MM-dd');

  switch (filter) {
    case 'all':
      return null;
    case 'today': {
      const from = formatForDB(new Date(), true);
      const to = formatForDB(new Date(), false);
      return { from, to };
    }
    case 'yesterday': {
      const y = subDays(today, 1);
      const from = formatForDB(new Date(y), true);
      const to = formatForDB(new Date(y), false);
      return { from, to };
    }
    case 'this-week': {
      const from = formatForDB(startOfWeek(today, { weekStartsOn: 1 }), true);
      const to = formatForDB(endOfWeek(today, { weekStartsOn: 1 }), false);
      return { from, to };
    }
    case 'last-week': {
      const lastWeekStart = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });
      const lastWeekEnd = endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });
      const from = formatForDB(lastWeekStart, true);
      const to = formatForDB(lastWeekEnd, false);
      return { from, to };
    }
    case 'last-7-days': {
      const from = formatForDB(subDays(today, 7), true);
      const to = formatForDB(today, false);
      return { from, to };
    }
    case 'this-month': {
      const from = formatForDB(startOfMonth(today), true);
      const to = formatForDB(endOfMonth(today), false);
      return { from, to };
    }
    case 'last-month': {
      const lastMonth = subMonths(today, 1);
      const from = formatForDB(startOfMonth(lastMonth), true);
      const to = formatForDB(endOfMonth(lastMonth), false);
      return { from, to };
    }
    case 'last-30-days': {
      const from = formatForDB(subDays(today, 30), true);
      const to = formatForDB(today, false);
      return { from, to };
    }
    case 'this-quarter': {
      const from = formatForDB(startOfQuarter(today), true);
      const to = formatForDB(endOfQuarter(today), false);
      return { from, to };
    }
    case 'last-quarter': {
      const lastQuarter = subQuarters(today, 1);
      const from = formatForDB(startOfQuarter(lastQuarter), true);
      const to = formatForDB(endOfQuarter(lastQuarter), false);
      return { from, to };
    }
    case 'last-90-days': {
      const from = formatForDB(subDays(today, 90), true);
      const to = formatForDB(today, false);
      return { from, to };
    }
    case 'last-180-days': {
      const from = formatForDB(subDays(today, 180), true);
      const to = formatForDB(today, false);
      return { from, to };
    }
    case 'this-year': {
      const from = formatForDB(startOfYear(today), true);
      const to = formatForDB(endOfYear(today), false);
      return { from, to };
    }
    case 'last-year': {
      const lastYear = subYears(today, 1);
      const from = formatForDB(startOfYear(lastYear), true);
      const to = formatForDB(endOfYear(lastYear), false);
      return { from, to };
    }
    case 'custom': {
      if (!customRange) return null;
      if (useFullDay) {
        const from = customRange.from ? new Date(customRange.from + 'T00:00:00.000Z').toISOString() : '';
        const to = customRange.to ? new Date(customRange.to + 'T23:59:59.999Z').toISOString() : '';
        return { from, to };
      } else {
        return { from: customRange.from, to: customRange.to };
      }
    }
    default:
      return null;
  }
};
