
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import { AlertProvider } from '@/contexts/AlertContext';
import { Toaster } from '@/components/ui/toaster';
import ProtectedRoute from '@/components/ProtectedRoute';
import RoleProtectedRoute from '@/components/RoleProtectedRoute';
import Layout from '@/components/Layout';
import Auth from '@/pages/Auth';
import Index from '@/pages/Index';
import Dashboard from '@/pages/Dashboard';
import Clients from '@/pages/Clients';
import ClientForm from '@/pages/ClientForm';
import ClientDetail from '@/pages/ClientDetail';
import ClientEdit from '@/pages/ClientEdit';
import Investments from '@/pages/Investments';
import InvestmentForm from '@/pages/InvestmentForm';
import InvestmentDetail from '@/pages/InvestmentDetail';
import InvestmentEdit from '@/pages/InvestmentEdit';
import Schemes from '@/pages/Schemes';
import SchemeForm from '@/pages/SchemeForm';
import SchemeDetail from '@/pages/SchemeDetail';
import SchemeEdit from '@/pages/SchemeEdit';
import Calculator from '@/pages/Calculator';
import Reports from '@/pages/Reports';
import Settings from '@/pages/Settings';
import Alerts from '@/pages/Alerts';
import Transactions from '@/pages/Transactions';
import TransactionForm from '@/pages/TransactionForm';
import Accounts from '@/pages/Accounts';
import AccountForm from '@/pages/AccountForm';
import AccountDetail from '@/pages/AccountDetail';
import AccountEdit from '@/pages/AccountEdit';
import NotFound from '@/pages/NotFound';
import TransactionDetail from '@/pages/TransactionDetail';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <AlertProvider>
          <Router>
            <Routes>
              <Route path="/auth" element={<Auth />} />
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<Index />} />
                <Route path="dashboard" element={
                  <RoleProtectedRoute module="dashboard">
                    <Dashboard />
                  </RoleProtectedRoute>
                } />
                <Route path="clients" element={
                  <RoleProtectedRoute module="clients">
                    <Clients />
                  </RoleProtectedRoute>
                } />
                <Route path="clients/new" element={
                  <RoleProtectedRoute module="clients" action="add">
                    <ClientForm />
                  </RoleProtectedRoute>
                } />
                <Route path="clients/:id" element={
                  <RoleProtectedRoute module="clients">
                    <ClientDetail />
                  </RoleProtectedRoute>
                } />
                <Route path="clients/:id/edit" element={
                  <RoleProtectedRoute module="clients" action="edit">
                    <ClientEdit />
                  </RoleProtectedRoute>
                } />
                <Route path="investments" element={
                  <RoleProtectedRoute module="investments">
                    <Investments />
                  </RoleProtectedRoute>
                } />
                <Route path="investments/new" element={
                  <RoleProtectedRoute module="investments" action="add">
                    <InvestmentForm />
                  </RoleProtectedRoute>
                } />
                <Route path="investments/:id" element={
                  <RoleProtectedRoute module="investments">
                    <InvestmentDetail />
                  </RoleProtectedRoute>
                } />
                <Route path="investments/:id/edit" element={
                  <RoleProtectedRoute module="investments" action="edit">
                    <InvestmentEdit />
                  </RoleProtectedRoute>
                } />
                <Route path="schemes" element={
                  <RoleProtectedRoute module="schemes">
                    <Schemes />
                  </RoleProtectedRoute>
                } />
                <Route path="schemes/new" element={
                  <RoleProtectedRoute module="schemes" action="add">
                    <SchemeForm />
                  </RoleProtectedRoute>
                } />
                <Route path="schemes/:id" element={
                  <RoleProtectedRoute module="schemes">
                    <SchemeDetail />
                  </RoleProtectedRoute>
                } />
                <Route path="schemes/:id/edit" element={
                  <RoleProtectedRoute module="schemes" action="edit">
                    <SchemeEdit />
                  </RoleProtectedRoute>
                } />
                <Route path="accounts" element={
                  <RoleProtectedRoute module="accounts">
                    <Accounts />
                  </RoleProtectedRoute>
                } />
                <Route path="accounts/new" element={
                  <RoleProtectedRoute module="accounts" action="add">
                    <AccountForm />
                  </RoleProtectedRoute>
                } />
                <Route path="accounts/:id" element={
                  <RoleProtectedRoute module="accounts">
                    <AccountDetail />
                  </RoleProtectedRoute>
                } />
                <Route path="accounts/:id/edit" element={
                  <RoleProtectedRoute module="accounts" action="edit">
                    <AccountEdit />
                  </RoleProtectedRoute>
                } />
                <Route path="transactions" element={
                  <RoleProtectedRoute module="transactions">
                    <Transactions />
                  </RoleProtectedRoute>
                } />
                <Route path="transactions/new" element={
                  <RoleProtectedRoute module="transactions" action="add">
                    <TransactionForm />
                  </RoleProtectedRoute>
                } />
                <Route path="transactions/:id" element={
                  <RoleProtectedRoute module="transactions">
                    <TransactionDetail />
                  </RoleProtectedRoute>
                } />
                <Route path="calculator" element={
                  <RoleProtectedRoute module="calculator">
                    <Calculator />
                  </RoleProtectedRoute>
                } />
                <Route path="reports" element={
                  <RoleProtectedRoute module="reports">
                    <Reports />
                  </RoleProtectedRoute>
                } />
                <Route path="settings" element={
                  <RoleProtectedRoute module="settings">
                    <Settings />
                  </RoleProtectedRoute>
                } />
                <Route path="alerts" element={
                  <RoleProtectedRoute module="alerts">
                    <Alerts />
                  </RoleProtectedRoute>
                } />
              </Route>
              <Route path="*" element={<NotFound />} />
            </Routes>
            <Toaster />
          </Router>
        </AlertProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
