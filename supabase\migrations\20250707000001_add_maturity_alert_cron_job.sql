-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS http;

-- Create or replace the maturity alert function
CREATE OR REPLACE FUNCTION public.send_maturity_alerts_email()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    alert_days integer;
    email_enabled boolean;
    target_date date;
    investment_record record;
    email_count integer := 0;
    result_json json;
    sendgrid_api_key text;
    sendgrid_sender_email text;
    http_response http_response;
BEGIN
    -- Get notification settings
    SELECT 
        ns.alert_days_before, 
        ns.email_enabled,
        ns.sendgrid_api_key,
        ns.sendgrid_sender_email
    INTO 
        alert_days, 
        email_enabled,
        sendgrid_api_key,
        sendgrid_sender_email
    FROM notification_settings ns
    LIMIT 1;
   
    -- Check if email is enabled
    IF NOT email_enabled THEN
        RETURN json_build_object(
            'success', false,
            'message', 'Email notifications are disabled'
        );
    END IF;
    
    -- Check if SendGrid credentials are available
    IF sendgrid_api_key IS NULL OR sendgrid_sender_email IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'message', 'SendGrid configuration missing in database settings'
        );
    END IF;
   
    -- Calculate target maturity date
    target_date := CURRENT_DATE + INTERVAL '1 day' * COALESCE(alert_days, 7);
   
    -- Loop through investments maturing on target date
    FOR investment_record IN
        SELECT
            i.id as investment_id,
            i.scheme_name,
            i.scheme_code,
            i.amount,
            i.maturity_amount,
            i.maturity_date,
            i.interest_rate,
            c.id as client_id,
            c.first_name,
            c.last_name,
            c.email,
            c.mobile_number
        FROM investments i
        JOIN clients c ON i.client_id = c.id
        WHERE i.status = 'active'
        AND i.maturity_date = target_date
        AND c.email IS NOT NULL
        AND c.is_deleted = false
        -- Check if we haven't already sent an alert today
        AND NOT EXISTS (
            SELECT 1 FROM alerts a 
            WHERE a.investment_id = i.id 
            AND a.alert_type = 'maturity_reminder'
            AND a.alert_date = CURRENT_DATE
        )
    LOOP
        -- Send email via Twilio SendGrid API
        SELECT * FROM http((
            'POST',
            'https://api.sendgrid.com/v3/mail/send',
            ARRAY[
                http_header('Authorization', 'Bearer ' || sendgrid_api_key),
                http_header('Content-Type', 'application/json')
            ],
            'application/json',
            json_build_object(
                'personalizations', json_build_array(
                    json_build_object(
                        'to', json_build_array(
                            json_build_object(
                                'email', investment_record.email,
                                'name', investment_record.first_name || ' ' || investment_record.last_name
                            )
                        )
                    )
                ),
                'from', json_build_object(
                    'email', sendgrid_sender_email,
                    'name', 'Investment Management Team'
                ),
                'subject', '🔔 Investment Maturity Alert - ' || investment_record.scheme_name,
                'content', json_build_array(
                    json_build_object(
                        'type', 'text/html',
                        'value', '<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #f59e0b, #d97706); padding: 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 24px; }
        .content { padding: 30px; }
        .alert-badge { background: #fef3c7; color: #92400e; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; display: inline-block; margin-bottom: 20px; }
        .investment-card { background: #fef3c7; border-radius: 8px; padding: 20px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        .detail-row { margin: 8px 0; }
        .label { font-weight: bold; color: #374151; }
        .value { color: #059669; font-weight: bold; }
        .footer { background: #1f2937; padding: 20px; text-align: center; color: #9ca3af; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Investment Maturity Alert</h1>
            <p>Your investment is maturing soon</p>
        </div>
        <div class="content">
            <div class="alert-badge">⏰ Maturing in ' || alert_days || ' days</div>
            <p>Dear ' || investment_record.first_name || ' ' || investment_record.last_name || ',</p>
            <p>Your investment is approaching its maturity date. Please review the details below:</p>
            <div class="investment-card">
                <h3>📈 Investment Details</h3>
                <div class="detail-row">
                    <span class="label">Scheme:</span> ' || investment_record.scheme_name || ' (' || investment_record.scheme_code || ')
                </div>
                <div class="detail-row">
                    <span class="label">Investment Amount:</span> <span class="value">₹' || investment_record.amount || '</span>
                </div>
                <div class="detail-row">
                    <span class="label">Maturity Amount:</span> <span class="value">₹' || investment_record.maturity_amount || '</span>
                </div>
                <div class="detail-row">
                    <span class="label">Maturity Date:</span> ' || investment_record.maturity_date || '
                </div>
                <div class="detail-row">
                    <span class="label">Interest Rate:</span> ' || investment_record.interest_rate || '% p.a.
                </div>
            </div>
            <p>🎉 Congratulations! Your investment has grown successfully.</p>
            <p>Please contact us to discuss your maturity options.</p>
            <p>Best regards,<br><strong>Investment Management Team</strong></p>
        </div>
        <div class="footer">
            <strong>Investment Pro</strong><br>
            This is an automated maturity alert.
        </div>
    </div>
</body>
</html>'
                    )
                )
            )::text
        )) INTO http_response;
       
        -- Log the alert
        INSERT INTO alerts (
            investment_id,
            client_id,
            alert_type,
            channel,
            status,
            message,
            alert_date
        ) VALUES (
            investment_record.investment_id,
            investment_record.client_id,
            'maturity_reminder',
            'email',
            CASE 
                WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'sent'
                ELSE 'failed'
            END,
            CASE 
                WHEN http_response.status >= 200 AND http_response.status < 300 THEN 'Maturity alert sent to ' || investment_record.email
                ELSE 'Failed to send alert: ' || http_response.content
            END,
            CURRENT_DATE
        );
       
        IF http_response.status >= 200 AND http_response.status < 300 THEN
            email_count := email_count + 1;
        END IF;
    END LOOP;
   
    -- Return result
    result_json := json_build_object(
        'success', true,
        'message', 'Processed ' || email_count || ' maturity alerts',
        'target_date', target_date,
        'alert_days_before', alert_days,
        'emails_sent', email_count
    );
   
    RETURN result_json;
END;
$$;

-- Schedule the cron job to run daily at 9 AM
SELECT cron.schedule(
  'send-maturity-alerts-email-daily',
  '0 9 * * *', -- Daily at 9 AM
  $$
  SELECT public.send_maturity_alerts_email();
  $$
);

-- Add a function to manually trigger the alert for testing
CREATE OR REPLACE FUNCTION public.trigger_maturity_alerts()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN public.send_maturity_alerts_email();
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.trigger_maturity_alerts() TO authenticated;