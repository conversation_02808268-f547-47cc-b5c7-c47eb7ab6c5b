import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Plus, Search, Eye, Edit, Trash2, Users, TrendingUp, Calendar, User, ArrowUpDown, X, FileText } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { formatDisplayDate } from '@/utils/dateFormat';
import { exportToPDF } from '@/utils/pdfExport';

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  city: string;
  state: string;
  village?: string;
  cif_id: string;
  created_at: string;
  investment_count?: number;
  total_investment?: number;
  maturity_this_month?: boolean;
}

const Clients: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [filter, setFilter] = useState(searchParams.get('filter') || 'all');
  const [dateFilter, setDateFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(parseInt(searchParams.get('limit') || '10'));
  const [totalCount, setTotalCount] = useState(0);
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>((searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc');
  const [stats, setStats] = useState({
    total: 0,
    withInvestments: 0,
    maturityThisMonth: 0,
    noInvestments: 0
  });
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth()
  // Handle explicit search action
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setCurrentPage(1); // Reset to first page on search
  };

  useEffect(() => {
    fetchClients();
  }, [searchTerm, filter, dateFilter, customDateRange, currentPage, itemsPerPage, sortBy, sortOrder]);

  useEffect(() => {
    fetchStats();
  }, []);

  // Update URL parameters when sorting changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    if (sortBy !== 'created_at') params.set('sortBy', sortBy);
    else params.delete('sortBy');
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    else params.delete('sortOrder');
    setSearchParams(params);
  }, [sortBy, sortOrder, searchParams, setSearchParams]);

  const fetchStats = async () => {
    try {
      // Total clients
      const { count: totalCount } = await supabase
        .from('clients')
        .select('id', { count: 'exact', head: true })
        .eq('is_deleted', false);

      // Clients with at least one investment (as primary or secondary applicant)
      const { data: investedClients } = await supabase
        .from('investments')
        .select('client_id, second_applicant_id');
      const uniqueInvestedClients = new Set();
      (investedClients || []).forEach(inv => {
        if (inv.client_id) uniqueInvestedClients.add(inv.client_id);
        if (inv.second_applicant_id) uniqueInvestedClients.add(inv.second_applicant_id);
      });

      const startOfMonth = new Date(year, month, 1).toISOString(); // e.g., 2025-07-01T00:00:00.000Z
      const startOfNextMonth = new Date(year, month + 1, 1).toISOString(); // e.g., 2025-08-01T00:00:00.000Z

      const { data: maturityClients } = await supabase
        .from('investments')
        .select('client_id, second_applicant_id')
        .gte('maturity_date', startOfMonth)
        .lt('maturity_date', startOfNextMonth);
      const uniqueMaturityClients = new Set();
      (maturityClients || []).forEach(inv => {
        if (inv.client_id) uniqueMaturityClients.add(inv.client_id);
        if (inv.second_applicant_id) uniqueMaturityClients.add(inv.second_applicant_id);
      });

      const stats = {
        total: totalCount || 0,
        withInvestments: uniqueInvestedClients.size,
        maturityThisMonth: uniqueMaturityClients.size,
        noInvestments: (totalCount || 0) - uniqueInvestedClients.size
      };
      setStats(stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchClients = async () => {
    try {
      setLoading(true);
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage - 1;
      let clientIds: string[] = [];
      let total = 0;
      let clientsData: Client[] = [];
      // Filtering logic
      if (filter === 'all') {
        let query = supabase
          .from('clients')
          .select('*', { count: 'exact' })
          .eq('is_deleted', false);
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase().trim();
          query = query.or(`first_name.ilike.%${searchLower}%,last_name.ilike.%${searchLower}%,email.ilike.%${searchLower}%,mobile_number.ilike.%${searchLower}%`);
        }
        // Enhanced date filter
        const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
        if (dateRange) {
          query = query.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
        }
        const { data, error, count } = await query.order(sortBy, { ascending: sortOrder === 'asc' }).range(startIndex, endIndex);
        if (error) throw error;
        clientsData = data || [];
        total = count || 0;
      } else if (filter === 'invested') {
        // Get unique client IDs with investments (as primary or secondary applicant)
        let invQuery = supabase.from('investments').select('client_id, second_applicant_id');
        const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
        if (dateRange) {
          invQuery = invQuery.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
        }
        const { data: invData, error: invError } = await invQuery;
        if (invError) throw invError;
        const uniqueClientIds = new Set<string>();
        (invData || []).forEach(inv => {
          if (inv.client_id) uniqueClientIds.add(inv.client_id);
          if (inv.second_applicant_id) uniqueClientIds.add(inv.second_applicant_id);
        });
        clientIds = Array.from(uniqueClientIds);
        // Apply search filter
        if (searchTerm && clientIds.length > 0) {
          const { data: searchClients } = await supabase
            .from('clients')
            .select('id, first_name, last_name, email, mobile_number')
            .in('id', clientIds);
          const searchLower = searchTerm.toLowerCase().trim();
          clientIds = (searchClients || []).filter(client =>
            (client.first_name && client.first_name.toLowerCase().includes(searchLower)) ||
            (client.last_name && client.last_name.toLowerCase().includes(searchLower)) ||
            (client.email && client.email.toLowerCase().includes(searchLower)) ||
            (client.mobile_number && client.mobile_number.toLowerCase().includes(searchLower))
          ).map(c => c.id);
        }
        total = clientIds.length;
        const paginatedIds = clientIds.slice(startIndex, endIndex + 1);
        if (paginatedIds.length > 0) {
          const { data, error } = await supabase
            .from('clients')
            .select('*')
            .in('id', paginatedIds)
            .order(sortBy, { ascending: sortOrder === 'asc' });
          if (error) throw error;
          clientsData = data || [];
        } else {
          clientsData = [];
        }
      } else if (filter === 'no-investment') {
        // Get all client IDs
        const { data: allClients } = await supabase
          .from('clients')
          .select('id')
          .eq('is_deleted', false);
        const allIds = (allClients || []).map(c => c.id);
        // Get invested client IDs (as primary or secondary applicant)
        let invQuery = supabase.from('investments').select('client_id, second_applicant_id');
        const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
        if (dateRange) {
          invQuery = invQuery.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
        }
        const { data: investedClients } = await invQuery;
        const investedIds = new Set<string>();
        (investedClients || []).forEach(inv => {
          if (inv.client_id) investedIds.add(inv.client_id);
          if (inv.second_applicant_id) investedIds.add(inv.second_applicant_id);
        });
        clientIds = allIds.filter(id => !investedIds.has(id));
        // Apply search filter
        if (searchTerm && clientIds.length > 0) {
          const { data: searchClients } = await supabase
            .from('clients')
            .select('id, first_name, last_name, email, mobile_number')
            .in('id', clientIds);
          const searchLower = searchTerm.toLowerCase().trim();
          clientIds = (searchClients || []).filter(client =>
            (client.first_name && client.first_name.toLowerCase().includes(searchLower)) ||
            (client.last_name && client.last_name.toLowerCase().includes(searchLower)) ||
            (client.email && client.email.toLowerCase().includes(searchLower)) ||
            (client.mobile_number && client.mobile_number.toLowerCase().includes(searchLower))
          ).map(c => c.id);
        }
        total = clientIds.length;
        const paginatedIds = clientIds.slice(startIndex, endIndex + 1);
        if (paginatedIds.length > 0) {
          const { data, error } = await supabase
            .from('clients')
            .select('*')
            .in('id', paginatedIds)
            .order(sortBy, { ascending: sortOrder === 'asc' });
          if (error) throw error;
          clientsData = data || [];
        } else {
          clientsData = [];
        }
      } else if (filter === 'maturity') {
        // Get unique client IDs with investments maturing this month (backend only)
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth();
        const startOfMonth = new Date(year, month, 1).toISOString();
        const startOfNextMonth = new Date(year, month + 1, 1).toISOString();
        let invQuery = supabase.from('investments').select('client_id, second_applicant_id')
          .gte('maturity_date', startOfMonth)
          .lt('maturity_date', startOfNextMonth);
        const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
        if (dateRange) {
          invQuery = invQuery.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
        }
        const { data: maturityClients } = await invQuery;
        const uniqueMaturityClientIds = new Set<string>();
        (maturityClients || []).forEach(inv => {
          if (inv.client_id) uniqueMaturityClientIds.add(inv.client_id);
          if (inv.second_applicant_id) uniqueMaturityClientIds.add(inv.second_applicant_id);
        });
        clientIds = Array.from(uniqueMaturityClientIds);
        // Apply search filter
        if (searchTerm && clientIds.length > 0) {
          const { data: searchClients } = await supabase
            .from('clients')
            .select('id, first_name, last_name, email, mobile_number')
            .in('id', clientIds);
          const searchLower = searchTerm.toLowerCase().trim();
          clientIds = (searchClients || []).filter(client =>
            (client.first_name && client.first_name.toLowerCase().includes(searchLower)) ||
            (client.last_name && client.last_name.toLowerCase().includes(searchLower)) ||
            (client.email && client.email.toLowerCase().includes(searchLower)) ||
            (client.mobile_number && client.mobile_number.toLowerCase().includes(searchLower))
          ).map(c => c.id);
        }
        total = clientIds.length;
        const paginatedIds = clientIds.slice(startIndex, endIndex + 1);
        if (paginatedIds.length > 0) {
          const { data, error } = await supabase
            .from('clients')
            .select('*')
            .in('id', paginatedIds)
            .order(sortBy, { ascending: sortOrder === 'asc' });
          if (error) throw error;
          clientsData = data || [];
        } else {
          clientsData = [];
        }
      }
      setTotalCount(total);
      // Fetch investment stats for the current page clients only
      const clientsWithStats = await Promise.all(
        (clientsData || []).map(async (client) => {
          // Get investments where client is primary or secondary applicant
          const { data: primaryInvestments } = await supabase
            .from('investments')
            .select('amount, maturity_date')
            .eq('client_id', client.id);

          const { data: secondaryInvestments } = await supabase
            .from('investments')
            .select('amount, maturity_date')
            .eq('second_applicant_id', client.id);

          const allInvestments = [...(primaryInvestments || []), ...(secondaryInvestments || [])];
          const investmentCount = allInvestments.length;
          const totalInvestment = allInvestments.reduce((sum, inv) => sum + Number(inv.amount || 0), 0);
          const currentMonth = new Date().toISOString().slice(0, 7);
          const maturityThisMonth = allInvestments.some(inv =>
            inv.maturity_date && inv.maturity_date.startsWith(currentMonth)
          ) || false;
          return {
            ...client,
            investment_count: investmentCount,
            total_investment: totalInvestment,
            maturity_this_month: maturityThisMonth
          };
        })
      );
      setClients(clientsWithStats);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        title: "Error",
        description: "Failed to fetch clients",
        variant: "destructive",
      });
      setLoading(false);
    }
  };

  const handleDelete = async (clientId: string) => {
    if (!confirm('Are you sure you want to delete this client?')) return;

    try {
      const { error } = await supabase
        .from('clients')
        .update({ is_deleted: true })
        .eq('id', clientId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Client deleted successfully",
      });

      fetchClients();
    } catch (error) {
      console.error('Error deleting client:', error);
      toast({
        title: "Error",
        description: "Failed to delete client",
        variant: "destructive",
      });
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  const SortableHeader = ({ column, children, className = "" }: { column: string; children: React.ReactNode; className?: string }) => (
    <TableHead
      className={`cursor-pointer hover:bg-gray-100 select-none font-semibold ${className}`}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
        {sortBy === column && (
          <span className="text-xs text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  // Pagination logic
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page

    // Update URL parameters
    const params = new URLSearchParams(searchParams);
    params.set('limit', newItemsPerPage.toString());
    params.set('page', '1');
    setSearchParams(params);
  };

  const generateClientReport = async () => {
    // Use the existing client data which already has all fields from the * select
    const clientsWithDetails = clients.map(client => ({
      ...client,
      aadhar_number: (client as any).aadhar_number || 'N/A',
      pan_card: (client as any).pan_card_number || 'N/A',
      village: client.village || (client as any).address || `${client.city}, ${client.state}` || 'N/A'
    }));

    const filterSummary = [
      { label: 'Search Term', value: searchTerm || 'All', applied: !!searchTerm },
      { label: 'Filter Type', value: filter === 'all' ? 'All Clients' : filter.replace(/-/g, ' '), applied: filter !== 'all' },
      { label: 'Date Filter', value: dateFilter === 'all' ? 'All Dates' : dateFilter, applied: dateFilter !== 'all' },
      { label: 'Total Records', value: totalCount.toString(), applied: true }
    ];

    const tableContent = `
      <table class="report-table">
        <thead>
          <tr>
            <th>S.No</th>
            <th>Client Name</th>
            <th>Contact</th>
            <th>CIF ID</th>
            <th>Village</th>
            <th>Aadhar Number</th>
            <th>PAN Card</th>
            <th>Total Investment</th>
            <th>Status</th>
            <th>Join Date</th>
          </tr>
        </thead>
        <tbody>
          ${clientsWithDetails.map((client, index) => {
            const clientName = `${client.first_name} ${client.last_name}`.trim();
            const contactInfo = `${client.mobile_number}${client.email ? ` | ${client.email}` : ''}`;
            const status = [];
            if (client.maturity_this_month) status.push('Maturity This Month');
            if (client.investment_count! > 0) status.push('Invested');
            if (client.investment_count === 0) status.push('No Investment');
            
            return `
            <tr>
              <td>${(currentPage - 1) * itemsPerPage + index + 1}</td>
              <td><strong>${clientName}</strong></td>
              <td>${contactInfo}</td>
              <td>${client.cif_id || 'N/A'}</td>
              <td>${client.village}</td>
              <td>${client.aadhar_number}</td>
              <td>${client.pan_card}</td>
              <td>₹${client.total_investment!.toLocaleString()}</td>
              <td>${status.join(', ')}</td>
              <td>${formatDisplayDate(client.created_at)}</td>
            </tr>
          `;
          }).join('')}
        </tbody>
      </table>
    `;

    await exportToPDF({
      title: 'Clients Report',
      content: tableContent,
      filterSummary
    });
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading clients...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Clients</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={generateClientReport}>
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button onClick={() => navigate('/clients/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Client
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => setFilter('all')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-blue-100 rounded-lg">
                <Users className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Clients</p>
                <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'invested' ? 'ring-2 ring-green-500' : ''}`}
          onClick={() => setFilter('invested')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Invested Clients</p>
                <p className="text-lg md:text-2xl font-bold">{stats.withInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'maturity' ? 'ring-2 ring-orange-500' : ''}`}
          onClick={() => setFilter('maturity')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Maturity This Month</p>
                <p className="text-lg md:text-2xl font-bold">{stats.maturityThisMonth}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${filter === 'no-investment' ? 'ring-2 ring-gray-500' : ''}`}
          onClick={() => setFilter('no-investment')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-gray-100 rounded-lg">
                <User className="h-4 w-4 md:h-5 md:w-5 text-gray-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">No Investment</p>
                <p className="text-lg md:text-2xl font-bold">{stats.noInvestments}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4">

        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <div className="relative w-full">
              <Input
                placeholder="Search by name, email, or mobile..."
                value={searchInput}
                onChange={(e) => {
                  setSearchInput(e.target.value);
                  // Auto-search when clearing all text (empty search)
                  if (e.target.value === '') {
                    setSearchTerm('');
                    setCurrentPage(1);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearch();
                  }
                }}
                className="pl-10 pr-20"
                autoComplete="off"
              />
              {searchInput && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-16 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => {
                    setSearchInput('');
                    setSearchTerm('');
                    setCurrentPage(1);
                  }}
                  title="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 px-3"
                onClick={handleSearch}
              >
                Search
              </Button>
            </div>
          </div>
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Clients</SelectItem>
              <SelectItem value="invested">Invested Clients</SelectItem>
              <SelectItem value="maturity">Maturity This Month</SelectItem>
              <SelectItem value="no-investment">No Investment</SelectItem>
            </SelectContent>
          </Select>
          <div className="w-full lg:w-auto">

            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>
        </div>
      </div>

      {/* Clients List Table */}
      <Card>
        <CardHeader>
          <CardTitle>Clients ({totalCount} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {clients.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                      <SortableHeader column="first_name" className="min-w-[150px]">Name</SortableHeader>
                      <SortableHeader column="mobile_number" className="min-w-[140px]">Contact</SortableHeader>
                      <SortableHeader column="cif_id" className="min-w-[120px] hidden sm:table-cell">CIF ID</SortableHeader>
                      <SortableHeader column="city" className="min-w-[120px] hidden md:table-cell">Location</SortableHeader>
                      <TableHead className="min-w-[100px] text-center font-semibold">Investments</TableHead>
                      <TableHead className="min-w-[120px] text-right font-semibold">Total Amount</TableHead>
                      <TableHead className="min-w-[100px] hidden md:table-cell font-semibold">Status</TableHead>
                      <TableHead className="min-w-[120px] text-right sticky right-0 bg-gray-50 font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clients.map((client, index) => (
                      <TableRow key={client.id} className="hover:bg-gray-50">
                        <TableCell className="w-16 text-center font-medium">
                          <span className="text-sm font-bold text-blue-600">{(currentPage - 1) * itemsPerPage + index + 1}</span>
                        </TableCell>
                        <TableCell className="min-w-[150px]">
                          <div>
                            <div className="font-medium text-gray-900 truncate">
                              {client.first_name} {client.last_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              Joined {formatDisplayDate(client.created_at)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[140px]">
                          <div>
                            <div className="text-sm font-medium">{client.mobile_number}</div>
                            <div className="text-sm text-gray-500 truncate">{client.email || 'N/A'}</div>
                            {client.village && (
                              <div className="text-xs text-blue-600 truncate">📍 {client.village}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[120px] hidden sm:table-cell">
                          <div className="text-sm font-medium text-blue-600">
                            {client.cif_id || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[120px] hidden md:table-cell">
                          <div className="text-sm truncate">
                            {client.city}, {client.state}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[100px] text-center">
                          <div className="font-medium">{client.investment_count}</div>
                          {/* Show status badges on mobile when Status column is hidden */}
                          <div className="flex flex-wrap justify-center gap-1 mt-1 md:hidden">
                            {client.maturity_this_month && (
                              <Badge variant="outline" className="text-orange-600 border-orange-200 text-xs">
                                Maturity
                              </Badge>
                            )}
                            {client.investment_count! > 0 && (
                              <Badge variant="outline" className="text-green-600 border-green-200 text-xs">
                                Invested
                              </Badge>
                            )}
                            {client.investment_count === 0 && (
                              <Badge variant="outline" className="text-gray-600 border-gray-200 text-xs">
                                No Investment
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[120px] text-right">
                          <div className="font-medium text-green-600">
                            {client.total_investment! > 0
                              ? `₹${client.total_investment!.toLocaleString()}`
                              : '₹0'
                            }
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[100px] hidden md:table-cell">
                          <div className="flex flex-wrap gap-1">
                            {client.maturity_this_month && (
                              <Badge variant="outline" className="text-orange-600 border-orange-200 text-xs">
                                Maturity
                              </Badge>
                            )}
                            {client.investment_count! > 0 && (
                              <Badge variant="outline" className="text-green-600 border-green-200 text-xs">
                                Invested
                              </Badge>
                            )}
                            {client.investment_count === 0 && (
                              <Badge variant="outline" className="text-gray-600 border-gray-200 text-xs">
                                No Investment
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[120px] text-right sticky right-0 bg-white">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/clients/${client.id}`)}
                              className="h-8 w-8 p-0 hover:bg-blue-50"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/clients/${client.id}/edit`)}
                              className="h-8 w-8 p-0 hover:bg-blue-50"
                              title="Edit Client"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(client.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Delete Client"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Enhanced Responsive Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t bg-gray-50">
                  {/* Mobile Pagination */}
                  <div className="flex flex-col space-y-4 sm:hidden">
                    <div className="text-sm text-gray-600 text-center">
                      Page {currentPage} of {totalPages} ({totalCount} total results)
                    </div>
                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        <span className="text-xs">‹</span>
                        Previous
                      </Button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Page</span>
                        <select
                          value={currentPage}
                          onChange={(e) => handlePageChange(Number(e.target.value))}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm bg-white min-w-[60px] text-center"
                        >
                          {Array.from({ length: totalPages }, (_, i) => (
                            <option key={i + 1} value={i + 1}>{i + 1}</option>
                          ))}
                        </select>
                        <span className="text-sm text-gray-500">of {totalPages}</span>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        Next
                        <span className="text-xs">›</span>
                      </Button>
                    </div>
                  </div>

                  {/* Desktop Pagination */}
                  <div className="hidden sm:flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="text-sm text-gray-600 order-2 lg:order-1">
                      Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalCount)}</span> of <span className="font-medium">{totalCount}</span> results
                    </div>

                    <div className="flex items-center justify-center lg:justify-end gap-2 order-1 lg:order-2">
                      {/* First Page Button */}
                      {currentPage > 3 && totalPages > 7 && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(1)}
                            className="w-10 h-10 p-0"
                          >
                            1
                          </Button>
                          {currentPage > 4 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                        </>
                      )}

                      {/* Previous Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="text-sm">‹</span>
                        <span className="hidden md:inline">Previous</span>
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                          let page: number;

                          if (totalPages <= 7) {
                            page = i + 1;
                          } else if (currentPage <= 4) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 3) {
                            page = totalPages - 6 + i;
                          } else {
                            page = currentPage - 3 + i;
                          }

                          // Don't show if it's already covered by first/last
                          if ((currentPage > 3 && totalPages > 7 && page === 1) ||
                            (currentPage < totalPages - 2 && totalPages > 7 && page === totalPages)) {
                            return null;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`w-10 h-10 p-0 ${currentPage === page
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'hover:bg-gray-100'
                                }`}
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="hidden md:inline">Next</span>
                        <span className="text-sm">›</span>
                      </Button>

                      {/* Last Page Button */}
                      {currentPage < totalPages - 2 && totalPages > 7 && (
                        <>
                          {currentPage < totalPages - 3 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(totalPages)}
                            className="w-10 h-10 p-0"
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Items per page selector for larger screens */}
                  <div className="hidden xl:flex items-center justify-center mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                      <span>entries per page</span>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || filter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first client'
                }
              </p>
              {!searchTerm && filter === 'all' && (
                <Button onClick={() => navigate('/clients/new')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Client
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Clients;
