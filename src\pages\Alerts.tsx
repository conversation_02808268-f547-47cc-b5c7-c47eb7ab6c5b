import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Bell, CheckCircle, XCircle, Clock, Mail, RefreshCw, AlertTriangle, Search, MessageSquare, Edit, Send, Users, FileText, ArrowUpDown } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useSearchParams } from 'react-router-dom';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { debounce } from 'lodash';
import { formatDisplayDate } from '@/utils/dateFormat';

interface Alert {
  id: string;
  investment_id: string | null;
  client_id: string | null;
  alert_type: string;
  channel: string;
  status: string;
  message: string;
  alert_date: string;
  created_at: string | null;
  investments?: {
    scheme_name: string;
    clients: {
      first_name: string;
      last_name: string;
      email: string;
      mobile_number: string;
    };
  };
  clients?: {
    first_name: string;
    last_name: string;
    email: string;
    mobile_number: string;
  };
}

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  mobile_number: string;
  email: string;
}

interface MessageTemplate {
  id: string;
  name: string;
  message: string;
  created_at: string;
}

const Alerts: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [searchInput, setSearchInput] = useState(searchParams.get('search') || '');
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || 'all');
  const [typeFilter, setTypeFilter] = useState(searchParams.get('type') || 'all');
  const [dateFilter, setDateFilter] = useState(searchParams.get('date') || 'all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();
  const [sendingManualAlert, setSendingManualAlert] = useState(false);
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>((searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc');

  // Pagination
  const [itemsPerPage, setItemsPerPage] = useState(parseInt(searchParams.get('limit') || '10'));
  const currentPage = parseInt(searchParams.get('page') || '1');
  const [totalCount, setTotalCount] = useState(0);
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Manual SMS functionality states
  const [showSMSDialog, setShowSMSDialog] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [customMessage, setCustomMessage] = useState('');
  const [messageType, setMessageType] = useState<'template' | 'custom'>('template');
  const [sendingSMS, setSendingSMS] = useState(false);
  const [clientSearchTerm, setClientSearchTerm] = useState('');

  const [isSMSEnabled, setIsSMSEnabled] = useState<boolean | null>(null);

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
    }, 500),
    []
  );

  useEffect(() => {
    debouncedSearch(searchInput);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchInput, debouncedSearch]);

  const fetchAlerts = useCallback(async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('alerts')
        .select(`
          *,
          investments:investments!alerts_investment_id_fkey(
            scheme_name,
            clients:clients!investments_client_id_fkey(first_name, last_name, email, mobile_number)
          ),
          clients:clients!alerts_client_id_fkey(first_name, last_name, email, mobile_number)
        `, { count: 'exact' });

      // Apply search filter
      if (searchTerm) {
        query = query.or(`
          message.ilike.%${searchTerm}%,
          investments.scheme_name.ilike.%${searchTerm}%,
          investments.clients.first_name.ilike.%${searchTerm}%,
          investments.clients.last_name.ilike.%${searchTerm}%,
          investments.clients.email.ilike.%${searchTerm}%,
          clients.first_name.ilike.%${searchTerm}%,
          clients.last_name.ilike.%${searchTerm}%,
          clients.email.ilike.%${searchTerm}%
        `);
      }

      // Apply status filter
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      // Apply type filter
      if (typeFilter !== 'all') {
        query = query.eq('alert_type', typeFilter);
      }

      // Apply date filter
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        query = query.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      // The data already includes client_id from the database
      const transformedData = data || [];

      setAlerts(transformedData);
      setTotalCount(count || 0);

    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch alerts",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, statusFilter, typeFilter, dateFilter, customDateRange, sortBy, sortOrder, itemsPerPage]);

  useEffect(() => {
    fetchAlerts();
    fetchClients();
    fetchTemplates();
    fetchNotificationSettings();
  }, [fetchAlerts]);

  useEffect(() => {
    // Update URL with current filters
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (statusFilter !== 'all') params.set('status', statusFilter);
    if (typeFilter !== 'all') params.set('type', typeFilter);
    if (dateFilter !== 'all') params.set('date', dateFilter);
    if (sortBy !== 'created_at') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);
    if (currentPage !== 1) params.set('page', currentPage.toString());
    if (itemsPerPage !== 10) params.set('limit', itemsPerPage.toString());

    setSearchParams(params);
  }, [searchTerm, statusFilter, typeFilter, dateFilter, sortBy, sortOrder, currentPage, itemsPerPage, setSearchParams]);

  const fetchClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, first_name, last_name, mobile_number, email')
        .eq('is_deleted', false)
        .order('first_name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const fetchTemplates = async () => {
    try {
      const defaultTemplates: MessageTemplate[] = [
        {
          id: '1',
          name: 'Investment Confirmation',
          message: 'Dear {CLIENT_NAME}, your investment has been confirmed successfully. Thank you for choosing us!',
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Maturity Reminder',
          message: 'Dear {CLIENT_NAME}, your investment is maturing soon. Please contact us for further instructions.',
          created_at: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Payment Reminder',
          message: 'Dear {CLIENT_NAME}, your payment is due. Please make the payment at your earliest convenience.',
          created_at: new Date().toISOString()
        }
      ];
      setTemplates(defaultTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const fetchNotificationSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_settings')
        .select('sms_enabled')
        .single();
      if (error) throw error;
      setIsSMSEnabled(data?.sms_enabled ?? false);
    } catch (error) {
      setIsSMSEnabled(false); // fallback to disabled if error
      console.error('Error fetching notification settings:', error);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', '1');
      return newParams;
    });
  };

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', page.toString());
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('limit', newItemsPerPage.toString());
      newParams.set('page', '1');
      return newParams;
    });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const triggerManualAlert = async () => {
    setSendingManualAlert(true);
    try {
      const { data, error } = await supabase.functions.invoke('send-maturity-alerts');

      if (error) throw error;

      toast({
        title: "Manual Alert Triggered",
        description: data.message || "Maturity alerts have been processed",
      });

      fetchAlerts();
    } catch (error) {
      console.error('Error triggering manual alert:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to trigger manual alert",
        variant: "destructive",
      });
    } finally {
      setSendingManualAlert(false);
    }
  };

  const handleSendManualSMS = async () => {
    if (selectedClients.length === 0) {
      toast({
        title: "No Recipients",
        description: "Please select at least one client",
        variant: "destructive",
      });
      return;
    }

    let message = '';

    if (messageType === 'template' && selectedTemplate) {
      const template = templates.find(t => t.id === selectedTemplate);
      message = template?.message || '';
    } else if (messageType === 'custom' && customMessage.trim()) {
      message = customMessage.trim();
    }

    if (!message) {
      toast({
        title: "No Message",
        description: messageType === 'template' ? "Please select a template" : "Please enter a custom message",
        variant: "destructive",
      });
      return;
    }

    setSendingSMS(true);
    let successCount = 0;
    let failCount = 0;

    try {
      // Get Twilio configuration from database
      const { data: twilioSettings } = await supabase
        .from('notification_settings')
        .select('twilio_account_sid, twilio_auth_token, twilio_phone_number')
        .single();

      const accountSid = twilioSettings?.twilio_account_sid;
      const authToken = twilioSettings?.twilio_auth_token;
      const fromNumber = twilioSettings?.twilio_phone_number;

      if (!accountSid || !authToken || !fromNumber) {
        throw new Error('Twilio configuration missing in database settings.');
      }

      for (const clientId of selectedClients) {
        const client = clients.find(c => c.id === clientId);
        if (!client) continue;

        try {
          let mobileNumber = client.mobile_number.replace(/\D/g, '');
          if (mobileNumber.length === 10) {
            mobileNumber = '+91' + mobileNumber;
          } else if (!mobileNumber.startsWith('+')) {
            mobileNumber = '+' + mobileNumber;
          }

          const personalizedMessage = message.replace(/{CLIENT_NAME}/g, `${client.first_name} ${client.last_name}`);

          const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`, {
            method: 'POST',
            headers: {
              'Authorization': 'Basic ' + btoa(`${accountSid}:${authToken}`),
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              To: mobileNumber,
              From: fromNumber,
              Body: personalizedMessage
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to send SMS');
          }

          await response.json();
          successCount++;

          await supabase.from('alerts').insert({
            investment_id: null,
            client_id: clientId,
            alert_type: 'manual_sms',
            channel: 'sms',
            status: 'sent',
            message: personalizedMessage,
            alert_date: new Date().toISOString().split('T')[0]
          });

        } catch (clientError) {
          console.error(`Error sending SMS to ${client.first_name} ${client.last_name}:`, clientError);
          failCount++;

          await supabase.from('alerts').insert({
            investment_id: null,
            client_id: clientId,
            alert_type: 'manual_sms',
            channel: 'sms',
            status: 'failed',
            message: `Failed to send: ${message.replace(/{CLIENT_NAME}/g, `${client.first_name} ${client.last_name}`)}`,
            alert_date: new Date().toISOString().split('T')[0]
          });
        }
      }

      if (successCount > 0) {
        toast({
          title: "SMS Sent Successfully",
          description: `${successCount} messages sent successfully${failCount > 0 ? `, ${failCount} failed` : ''}`,
        });
      }

      if (failCount > 0 && successCount === 0) {
        toast({
          title: "SMS Failed",
          description: `Failed to send ${failCount} messages`,
          variant: "destructive",
        });
      }

      setShowSMSDialog(false);
      setSelectedClients([]);
      setSelectedTemplate('');
      setCustomMessage('');
      setMessageType('template');
      setClientSearchTerm('');

      fetchAlerts();

    } catch (error) {
      console.error('Error sending SMS:', error);
      toast({
        title: "SMS Failed",
        description: error.message || "Failed to send SMS. Please check your configuration.",
        variant: "destructive",
      });
    } finally {
      setSendingSMS(false);
    }
  };

  // Filter clients based on search term
  const filteredClients = clients.filter(client => {
    const searchLower = clientSearchTerm.toLowerCase();
    const fullName = `${client.first_name} ${client.last_name}`.toLowerCase();
    const mobile = client.mobile_number.toLowerCase();
    const email = client.email?.toLowerCase() || '';

    return fullName.includes(searchLower) ||
      mobile.includes(searchLower) ||
      email.includes(searchLower);
  });

  const handleSelectAllClients = () => {
    const filteredClientIds = filteredClients.map(c => c.id);
    const allFilteredSelected = filteredClientIds.every(id => selectedClients.includes(id));

    if (allFilteredSelected) {
      // Deselect all filtered clients
      setSelectedClients(prev => prev.filter(id => !filteredClientIds.includes(id)));
    } else {
      // Select all filtered clients
      setSelectedClients(prev => [...new Set([...prev, ...filteredClientIds])]);
    }
  };

  const handleClientSelection = (clientId: string) => {
    setSelectedClients(prev =>
      prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    );
  };

  const isSendButtonEnabled = () => {
    if (sendingSMS || selectedClients.length === 0) return false;

    if (messageType === 'template') {
      return selectedTemplate !== '';
    } else {
      return customMessage.trim() !== '';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'maturity_reminder':
        return 'bg-blue-100 text-blue-800';
      case 'investment_confirmation':
        return 'bg-green-100 text-green-800';
      case 'manual_sms':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const SortableHeader = ({ column, children }: { column: string; children: React.ReactNode }) => (
    <TableHead
      className="cursor-pointer hover:bg-gray-100 select-none font-semibold"
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
        {sortBy === column && (
          <span className="text-xs text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </TableHead>
  );

  const stats = {
    total: totalCount,
    sent: alerts.filter(a => a.status === 'sent').length,
    failed: alerts.filter(a => a.status === 'failed').length,
    pending: alerts.filter(a => a.status === 'pending').length
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading alerts...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold">Alert Management</h1>
        <div className="flex gap-2">
          <Dialog open={showSMSDialog} onOpenChange={setShowSMSDialog}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="w-full sm:w-auto border-2 border-green-200 bg-black text-white hover:bg-green-50 hover:text-black"
                size="sm"
                disabled={isSMSEnabled === false}
                title={isSMSEnabled === false ? "SMS sending is disabled in notification settings" : undefined}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Send Manual SMS</span>
                <span className="sm:hidden">Send SMS</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <MessageSquare className="h-5 w-5" />
                  <span className="hidden sm:inline">Send Manual SMS Alert</span>
                  <span className="sm:hidden">Send SMS</span>
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                <div className="space-y-3">
                  <Label className="text-base font-medium">Message Type</Label>
                  <Select
                    value={messageType}
                    onValueChange={(value: 'template' | 'custom') => {
                      setMessageType(value);
                      if (value === 'template') {
                        setCustomMessage('');
                      } else {
                        setSelectedTemplate('');
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="template">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Use Template
                        </div>
                      </SelectItem>
                      <SelectItem value="custom">
                        <div className="flex items-center gap-2">
                          <Edit className="h-4 w-4" />
                          Custom Message
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {messageType === 'template' && (
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Select Template</Label>
                    <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a message template" />
                      </SelectTrigger>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedTemplate && (
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <Label className="text-sm font-medium text-gray-700">Preview:</Label>
                        <p className="text-sm text-gray-600 mt-1">
                          {templates.find(t => t.id === selectedTemplate)?.message.replace(/{CLIENT_NAME}/g, '[Client Name]')}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {messageType === 'custom' && (
                  <div className="space-y-3">
                    <Label htmlFor="customMessage" className="text-base font-medium">Custom Message</Label>
                    <Textarea
                      id="customMessage"
                      placeholder="Enter your custom message here..."
                      value={customMessage}
                      onChange={(e) => setCustomMessage(e.target.value)}
                      rows={4}
                      className="resize-none w-full"
                    />
                    <p className="text-xs text-gray-500 break-words">
                      Tip: Use {'{CLIENT_NAME}'} to automatically insert the client's name
                    </p>
                    {customMessage.trim() && (
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <Label className="text-sm font-medium text-gray-700">Preview:</Label>
                        <p className="text-sm text-gray-600 mt-1 break-words">
                          {customMessage.replace(/{CLIENT_NAME}/g, '[Client Name]')}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <Label className="text-base font-medium">Select Recipients</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAllClients}
                      className="w-full sm:w-auto"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      <span className="hidden sm:inline">
                        {filteredClients.length > 0 && filteredClients.every(client => selectedClients.includes(client.id)) ? 'Deselect All' : 'Select All'}
                      </span>
                      <span className="sm:hidden">
                        {filteredClients.length > 0 && filteredClients.every(client => selectedClients.includes(client.id)) ? 'Deselect' : 'Select All'}
                      </span>
                    </Button>
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search clients by name, mobile, or email..."
                      value={clientSearchTerm}
                      onChange={(e) => setClientSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="max-h-48 overflow-y-auto border rounded-lg p-3 space-y-2">
                    {filteredClients.length > 0 ? (
                      filteredClients.map((client) => (
                        <div key={client.id} className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            id={`client-${client.id}`}
                            checked={selectedClients.includes(client.id)}
                            onChange={() => handleClientSelection(client.id)}
                            className="rounded border-gray-300 flex-shrink-0"
                          />
                          <label htmlFor={`client-${client.id}`} className="flex-1 cursor-pointer min-w-0">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                              <span className="font-medium truncate">{client.first_name} {client.last_name}</span>
                              <span className="text-sm text-gray-500 flex-shrink-0">{client.mobile_number}</span>
                            </div>
                          </label>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-gray-500">
                        <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm">No clients found matching your search</p>
                        {clientSearchTerm && (
                          <p className="text-xs mt-1">Try adjusting your search terms</p>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row sm:justify-between gap-2 text-sm text-gray-600">
                    <span>
                      {selectedClients.length} of {clients.length} clients selected
                    </span>
                    {clientSearchTerm && (
                      <span>
                        Showing {filteredClients.length} of {clients.length} clients
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row sm:justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowSMSDialog(false);
                      setClientSearchTerm('');
                    }}
                    className="w-full sm:w-auto order-2 sm:order-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSendManualSMS}
                    disabled={!isSendButtonEnabled()}
                    className="w-full sm:w-auto bg-green-600 hover:bg-green-700 order-1 sm:order-2"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">
                      {sendingSMS ? 'Sending...' : `Send SMS (${selectedClients.length})`}
                    </span>
                    <span className="sm:hidden">
                      {sendingSMS ? 'Sending...' : `Send (${selectedClients.length})`}
                    </span>
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>


        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'all' ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => setStatusFilter('all')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-blue-100 rounded-lg">
                <Bell className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Total Alerts</p>
                <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'sent' ? 'ring-2 ring-green-500' : ''}`}
          onClick={() => setStatusFilter('sent')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Sent Successfully</p>
                <p className="text-lg md:text-2xl font-bold">{stats.sent}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'failed' ? 'ring-2 ring-red-500' : ''}`}
          onClick={() => setStatusFilter('failed')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-red-100 rounded-lg">
                <XCircle className="h-4 w-4 md:h-5 md:w-5 text-red-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Failed</p>
                <p className="text-lg md:text-2xl font-bold">{stats.failed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer hover:shadow-md transition-shadow ${statusFilter === 'pending' ? 'ring-2 ring-yellow-500' : ''}`}
          onClick={() => setStatusFilter('pending')}
        >
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center gap-2 md:gap-3">
              <div className="p-1.5 md:p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-4 w-4 md:h-5 md:w-5 text-yellow-600" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs md:text-sm text-gray-600 truncate">Pending</p>
                <p className="text-lg md:text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by client, scheme, or message..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className='flex flex-col sm:flex-row gap-4'>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="maturity_reminder">Maturity Reminder</SelectItem>
                <SelectItem value="investment_confirmation">Investment Confirmation</SelectItem>
                <SelectItem value="manual_sms">Manual SMS</SelectItem>
              </SelectContent>
            </Select>
          </div>


          <div className="w-full lg:w-auto">
            <EnhancedDateFilter
              value={dateFilter}
              onChange={setDateFilter}
              customRange={customDateRange}
              onCustomRangeChange={setCustomDateRange}
            />
          </div>
        </div>

      </div>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Alert History ({totalCount} total)</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {alerts.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="w-16 text-center font-semibold">S.No</TableHead>
                      <SortableHeader column="investments.clients.first_name">Client</SortableHeader>
                      <TableHead className="font-semibold">Investment</TableHead>
                      <SortableHeader column="alert_type">Alert Type</SortableHeader>
                      <TableHead className="font-semibold">Channel</TableHead>
                      <SortableHeader column="status">Status</SortableHeader>
                      <TableHead className="font-semibold">Message</TableHead>
                      <SortableHeader column="alert_date">Alert Date</SortableHeader>
                      <SortableHeader column="created_at">Created</SortableHeader>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {alerts.map((alert, index) => (
                      <TableRow key={alert.id}>
                        <TableCell className="w-16 text-center font-medium">
                          <span className="text-sm font-bold text-blue-600">{(currentPage - 1) * itemsPerPage + index + 1}</span>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {(() => {
                                // For manual SMS, get client info from direct client relationship
                                if (alert.alert_type === 'manual_sms' && alert.clients) {
                                  return `${alert.clients.first_name} ${alert.clients.last_name}`;
                                }
                                // For investment-related alerts, get from investments relationship
                                return `${alert.investments?.clients?.first_name || ''} ${alert.investments?.clients?.last_name || ''}`.trim() || 'N/A';
                              })()}
                            </div>
                            <div className="text-sm text-gray-500">
                              {(() => {
                                // For manual SMS, get email from direct client relationship
                                if (alert.alert_type === 'manual_sms' && alert.clients) {
                                  return alert.clients.email || alert.clients.mobile_number;
                                }
                                // For investment-related alerts, get from investments relationship
                                return alert.investments?.clients?.email || '';
                              })()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {alert.investments?.scheme_name || 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTypeColor(alert.alert_type)}>
                            {alert.alert_type.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-gray-400" />
                            {alert.channel}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(alert.status)}
                            <Badge className={getStatusColor(alert.status)}>
                              {alert.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs truncate" title={alert.message}>
                            {alert.message}
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatDisplayDate(alert.alert_date)}
                        </TableCell>
                        <TableCell>
                          {formatDisplayDate(alert.created_at)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Enhanced Responsive Pagination */}
              {totalPages > 1 && (
                <div className="p-4 border-t bg-gray-50">
                  {/* Mobile Pagination */}
                  <div className="flex flex-col space-y-4 sm:hidden">
                    <div className="text-sm text-gray-600 text-center">
                      Page {currentPage} of {totalPages} ({totalCount} total results)
                    </div>
                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        <span className="text-xs">‹</span>
                        Previous
                      </Button>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Page</span>
                        <select
                          value={currentPage}
                          onChange={(e) => handlePageChange(Number(e.target.value))}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm bg-white min-w-[60px] text-center"
                        >
                          {Array.from({ length: totalPages }, (_, i) => (
                            <option key={i + 1} value={i + 1}>{i + 1}</option>
                          ))}
                        </select>
                        <span className="text-sm text-gray-500">of {totalPages}</span>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-2 min-w-[80px]"
                      >
                        Next
                        <span className="text-xs">›</span>
                      </Button>
                    </div>
                  </div>

                  {/* Desktop Pagination */}
                  <div className="hidden sm:flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="text-sm text-gray-600 order-2 lg:order-1">
                      Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalCount)}</span> of <span className="font-medium">{totalCount}</span> results
                    </div>

                    <div className="flex items-center justify-center lg:justify-end gap-2 order-1 lg:order-2">
                      {/* First Page Button */}
                      {currentPage > 3 && totalPages > 7 && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(1)}
                            className="w-10 h-10 p-0"
                          >
                            1
                          </Button>
                          {currentPage > 4 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                        </>
                      )}

                      {/* Previous Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="text-sm">‹</span>
                        <span className="hidden md:inline">Previous</span>
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                          let page: number;

                          if (totalPages <= 7) {
                            page = i + 1;
                          } else if (currentPage <= 4) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 3) {
                            page = totalPages - 6 + i;
                          } else {
                            page = currentPage - 3 + i;
                          }

                          // Don't show if it's already covered by first/last
                          if ((currentPage > 3 && totalPages > 7 && page === 1) ||
                            (currentPage < totalPages - 2 && totalPages > 7 && page === totalPages)) {
                            return null;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`w-10 h-10 p-0 ${currentPage === page
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'hover:bg-gray-100'
                                }`}
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      {/* Next Button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1 px-3"
                      >
                        <span className="hidden md:inline">Next</span>
                        <span className="text-sm">›</span>
                      </Button>

                      {/* Last Page Button */}
                      {currentPage < totalPages - 2 && totalPages > 7 && (
                        <>
                          {currentPage < totalPages - 3 && (
                            <span className="px-2 text-gray-400">...</span>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(totalPages)}
                            className="w-10 h-10 p-0"
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Items per page selector for larger screens */}
                  <div className="hidden xl:flex items-center justify-center mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                      <span>entries per page</span>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No alerts found</h3>
              <p className="text-gray-500">
                {statusFilter !== 'all' || typeFilter !== 'all' || dateFilter !== 'all'
                  ? 'Try adjusting your filters'
                  : 'Alerts will appear here when they are sent'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Alerts;
