import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Download, FileSpreadsheet, Search, Filter, TrendingUp, Users, IndianRupee, Calendar, ArrowUpDown, Edit, X } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { EnhancedDateFilter, DateR<PERSON><PERSON>, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { debounce } from 'lodash';
import { formatDisplayDate } from '@/utils/dateFormat';
import { exportToPDF } from '@/utils/pdfExport';
import { formatAmountInIndianFormat } from '@/utils/formatters';


interface ReportFilters {
  searchTerm: string;
  dateFrom: string;
  dateTo: string;
  status: string;
  scheme: string;
  client: string;
  amountFrom: string;
  amountTo: string;
  transactionType: string;
}


interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  mobile_number: string;
  aadhar_number: string;
  pan_card_number: string;
  sb_account_number: string;
  created_at: string;
  village: string | null;
  cif_id: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  total_investment?: number;
}


interface Investment {
  id: string;
  client_id: string;
  second_applicant_id: string | null;
  scheme_name: string;
  scheme_code: string;
  amount: number;
  interest_rate: number;
  investment_date: string;
  start_date: string;
  maturity_date: string;
  maturity_amount: number;
  commission_percentage: number;
  tds_amount: number;
  status: string;
  lock_in_period_months: number;
  primary_applicant_cif_id: string | null;
  secondary_applicant_cif_id: string | null;
  remark: string | null;
  clients: {
    first_name: string;
    last_name: string;
    mobile_number: string;
    village: string | null;
    cif_id: string | null;
  };
  secondary_applicant?: {
    first_name: string;
    last_name: string;
    mobile_number: string;
    village: string | null;
    cif_id: string | null;
  } | null;
  applicant_type?: 'primary' | 'secondary';
}


interface Transaction {
  id: string;
  investment_id: string;
  amount: number;
  amount_type: string;
  transaction_date: string;
  reference_number: string;
  payment_mode: string;
  remark: string;
  investments: {
    scheme_name: string;
    scheme_code: string;
    primary_applicant_cif_id: string | null;
    secondary_applicant_cif_id: string | null;
    clients: {
      first_name: string;
      last_name: string;
      mobile_number: string;
      village: string | null;
      cif_id: string | null;
    };
    secondary_applicant?: {
      first_name: string;
      last_name: string;
      mobile_number: string;
      village: string | null;
      cif_id: string | null;
    } | null;
  };
}



const Reports: React.FC = () => {
  // Function to get color for amount type
  const getAmountTypeColor = (type: string) => {
    switch (type) {
      case 'investment': return 'bg-green-100 text-green-800 border-green-200';
      case 'reinvestment': return 'bg-green-100 text-green-800 border-green-200';
      case 'interest_payout': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'withdrawal': return 'bg-red-100 text-red-800 border-red-200';
      case 'penalty': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'commission': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  const [activeTab, setActiveTab] = useState('maturity');
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [schemes, setSchemes] = useState<{ id: string; name: string; scheme_code: string }[]>([]);
  const [allClients, setAllClients] = useState<{ id: string; first_name: string; last_name: string }[]>([]);


  const [filters, setFilters] = useState<ReportFilters>({
    searchTerm: '',
    dateFrom: '',
    dateTo: '',
    status: 'all',
    scheme: 'all',
    client: 'all',
    amountFrom: '',
    amountTo: '',
    transactionType: 'all',
  });

  interface Nominee {
    id: string;
    name: string;
    relation: string;
    birthdate: string | null;
    document_url: string | null;
  }

  const [selectedClientData, setSelectedClientData] = useState<{
    client: Client | null;
    investments: Investment[];
    transactions: Transaction[];
    nominees: Nominee[];
    totalInvestment: number;
    totalMaturity: number;
  }>({
    client: null,
    investments: [],
    transactions: [],
    nominees: [],
    totalInvestment: 0,
    totalMaturity: 0
  });

  const [searchInput, setSearchInput] = useState('');

  // Remarks editing state
  const [editingRemarks, setEditingRemarks] = useState<string | null>(null);
  const [remarksValue, setRemarksValue] = useState<string>('');

  const [dateFilter, setDateFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();


  // Remarks editing functions
  const handleEditRemarks = (investment: Investment) => {
    setEditingRemarks(investment.id);
    setRemarksValue(investment.remark || '');
  };

  const handleSaveRemarks = async (investmentId: string) => {
    try {
      const { error } = await supabase
        .from('investments')
        .update({ remark: remarksValue.trim() || null })
        .eq('id', investmentId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Remarks updated successfully",
      });

      setEditingRemarks(null);
      setRemarksValue('');
      fetchReportData(); // Refresh the data
    } catch (error) {
      console.error('Error updating remarks:', error);
      toast({
        title: "Error",
        description: "Failed to update remarks",
        variant: "destructive",
      });
    }
  };

  const handleCancelEditRemarks = () => {
    setEditingRemarks(null);
    setRemarksValue('');
  };

  useEffect(() => {
    fetchInitialData();
  }, []);


  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setFilters(prev => ({ ...prev, searchTerm: value }));
    }, 500),
    []
  );

  // Handle search input change
  useEffect(() => {
    debouncedSearch(searchInput);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchInput, debouncedSearch]);

  useEffect(() => {
    fetchReportData();

    // When switching to client_wise tab, run fetchClientWiseReport if a client is selected
    if (activeTab === 'client_wise' && filters.client !== 'all') {
      fetchClientWiseReport();
    }
  }, [activeTab, filters]);


  const fetchInitialData = async () => {
    try {
      const [schemesRes, clientsRes] = await Promise.all([
        supabase.from('schemes').select('id, name, scheme_code').eq('is_active', true),
        supabase.from('clients').select('id, first_name, last_name').eq('is_deleted', false)
      ]);


      if (schemesRes.error) throw schemesRes.error;
      if (clientsRes.error) throw clientsRes.error;


      setSchemes(schemesRes.data || []);
      setAllClients(clientsRes.data || []);
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  };


  const fetchReportData = async () => {
    setLoading(true);
    try {
      switch (activeTab) {
        case 'client':
          await fetchClientReport();
          break;
        case 'client_wise':
          await fetchClientWiseReport();
          break;
        case 'investment':
          await fetchInvestmentReport();
          break;
        case 'transaction':
          await fetchTransactionReport();
          break;
        case 'commission':
          await fetchCommissionReport();
          break;
        case 'maturity':
          await fetchMaturityReport();
          break;
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch report data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };


  const fetchClientReport = async () => {
    let query = supabase
      .from('clients')
      .select(`
        id,
        first_name,
        last_name,
        email,
        mobile_number,
        aadhar_number,
        pan_card_number,
        village,
        cif_id,
        address,
        city,
        state,
        pincode,
        created_at
      `)
      .eq('is_deleted', false);


    if (filters.searchTerm) {
      query = query.or(`first_name.ilike.%${filters.searchTerm}%,last_name.ilike.%${filters.searchTerm}%,email.ilike.%${filters.searchTerm}%`);
    }


    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (dateRange) {
      query = query.gte('created_at', dateRange.from).lte('created_at', dateRange.to);
    }


    const { data, error } = await query;
    if (error) throw error;


    // Fetch investment totals for each client
    const clientsWithInvestments = await Promise.all(
      (data || []).map(async (client) => {
        const { data: investments } = await supabase
          .from('investments')
          .select('amount')
          .eq('client_id', client.id);

        const totalInvestment = investments?.reduce((sum, inv) => sum + (inv.amount || 0), 0) || 0;

        return {
          ...client,
          total_investment: totalInvestment,
          sb_account_number: 'N/A'
        };
      })
    );

    // Apply client status filter
    let filteredClients = clientsWithInvestments;
    if (filters.status === 'invested') {
      filteredClients = clientsWithInvestments.filter(client => client.total_investment! > 0);
    } else if (filters.status === 'no-invested') {
      filteredClients = clientsWithInvestments.filter(client => client.total_investment === 0);
    }

    setClients(filteredClients);
  };


  const fetchInvestmentReport = async () => {
    let query = supabase
      .from('investments')
      .select(`
        *,
        clients!investments_client_id_fkey(
          first_name,
          last_name,
          mobile_number,
          village,
          cif_id
        ),
        secondary_applicant:clients!investments_second_applicant_id_fkey(
          first_name,
          last_name,
          mobile_number,
          village,
          cif_id
        )
      `)
      .order('investment_date', { ascending: false });


    if (filters.client !== 'all') {
      query = query.eq('client_id', filters.client);
    }


    if (filters.status !== 'all') {
      query = query.eq('status', filters.status);
    }


    const dateRange = getDateRangeValues(dateFilter, customDateRange, false);
    if (dateRange) {
      query = query.gte('investment_date', dateRange.from).lte('investment_date', dateRange.to);
    }


    if (filters.amountFrom) {
      query = query.gte('amount', parseFloat(filters.amountFrom));
    }


    if (filters.amountTo) {
      query = query.lte('amount', parseFloat(filters.amountTo));
    }


    const { data, error } = await query;
    if (error) throw error;


    setInvestments(data || []);
  };


  const fetchTransactionReport = async () => {
    let query = supabase
      .from('transactions')
      .select(`
        *,
        investments!transactions_investment_id_fkey(
          scheme_name,
          scheme_code,
          primary_applicant_cif_id,
          secondary_applicant_cif_id,
          clients!investments_client_id_fkey(
            first_name,
            last_name,
            mobile_number,
            village,
            cif_id
          ),
          secondary_applicant:clients!investments_second_applicant_id_fkey(
            first_name,
            last_name,
            mobile_number,
            village,
            cif_id
          )
        )
      `)
      .order('transaction_date', { ascending: false });


    if (filters.transactionType !== 'all') {
      query = query.eq('amount_type', filters.transactionType);
    }


    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (dateRange) {
      query = query.gte('transaction_date', dateRange.from).lte('transaction_date', dateRange.to);
    }


    const { data, error } = await query;
    if (error) throw error;


    setTransactions(data || []);
  };


  const fetchCommissionReport = async () => {
    await fetchInvestmentReport(); // Use same data as investment report
  };


  const fetchClientWiseReport = async () => {
    if (filters.client === 'all') {
      setSelectedClientData({
        client: null,
        investments: [],
        transactions: [],
        nominees: [],
        totalInvestment: 0,
        totalMaturity: 0
      });
      return;
    }

    try {
      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', filters.client)
        .single();

      if (clientError) throw clientError;

      // Fetch investments where client is primary applicant
      const { data: primaryInvestments, error: primaryError } = await supabase
        .from('investments')
        .select(`
          *,
          clients!investments_client_id_fkey(
            first_name,
            last_name,
            mobile_number,
            village,
            cif_id
          ),
          secondary_applicant:clients!investments_second_applicant_id_fkey(
            first_name,
            last_name,
            mobile_number,
            village,
            cif_id
          )
        `)
        .eq('client_id', filters.client)
        .eq('is_active', true)
        .order('investment_date', { ascending: false });

      if (primaryError) throw primaryError;

      // Fetch investments where client is secondary applicant
      const { data: secondaryInvestments, error: secondaryError } = await supabase
        .from('investments')
        .select(`
          *,
          clients!investments_client_id_fkey(
            first_name,
            last_name,
            mobile_number,
            village,
            cif_id
          ),
          secondary_applicant:clients!investments_second_applicant_id_fkey(
            first_name,
            last_name,
            mobile_number,
            village,
            cif_id
          )
        `)
        .eq('second_applicant_id', filters.client)
        .eq('is_active', true)
        .order('investment_date', { ascending: false });

      if (secondaryError) throw secondaryError;

      // Combine and mark investments with applicant type
      const allInvestments = [
        ...(primaryInvestments || []).map(inv => ({ ...inv, applicant_type: 'primary' as const })),
        ...(secondaryInvestments || []).map(inv => ({ ...inv, applicant_type: 'secondary' as const }))
      ];

      // Sort combined investments by investment date (newest first)
      allInvestments.sort((a, b) => new Date(b.investment_date).getTime() - new Date(a.investment_date).getTime());

      // Fetch transactions for both primary and secondary investments
      const allInvestmentIds = allInvestments.map(inv => inv.id);

      let transactionsData = [];
      if (allInvestmentIds.length > 0) {
        const { data: transactionResults, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            *,
            investments!transactions_investment_id_fkey(
              scheme_name,
              scheme_code,
              primary_applicant_cif_id,
              secondary_applicant_cif_id,
              clients!investments_client_id_fkey(
                first_name,
                last_name,
                mobile_number,
                village,
                cif_id
              ),
              secondary_applicant:clients!investments_second_applicant_id_fkey(
                first_name,
                last_name,
                mobile_number,
                village,
                cif_id
              )
            )
          `)
          .in('investment_id', allInvestmentIds)
          .order('transaction_date', { ascending: false });

        if (transactionsError) throw transactionsError;
        transactionsData = transactionResults || [];
      }

      // Fetch nominees
      const { data: nomineesData, error: nomineesError } = await supabase
        .from('nominees')
        .select('*')
        .eq('client_id', filters.client);

      if (nomineesError) throw nomineesError;

      // Calculate totals
      const totalInvestment = allInvestments.reduce((sum, inv) => sum + inv.amount, 0);
      const totalMaturity = allInvestments.reduce((sum, inv) => sum + inv.maturity_amount, 0);

      setSelectedClientData({
        client: {
          ...clientData,
          sb_account_number: 'N/A'
        },
        investments: allInvestments,
        transactions: transactionsData,
        nominees: nomineesData || [],
        totalInvestment,
        totalMaturity
      });

    } catch (error) {
      console.error('Error fetching client-wise report:', error);
      toast({
        title: "Error",
        description: "Failed to fetch client data",
        variant: "destructive",
      });
    }
  };

  const fetchMaturityReport = async () => {
    let query = supabase
      .from('investments')
      .select(`
        *,
        clients!investments_client_id_fkey(
          first_name,
          last_name,
          mobile_number,
          village,
          cif_id
        ),
        secondary_applicant:clients!investments_second_applicant_id_fkey(
          first_name,
          last_name,
          mobile_number,
          village,
          cif_id
        )
      `)
      .eq('is_active', true)  // Show all investments regardless of status
      .order('investment_date', { ascending: false });


    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (dateRange) {
      query = query.gte('maturity_date', dateRange.from).lte('maturity_date', dateRange.to);
    }


    const { data, error } = await query;
    if (error) throw error;


    setInvestments(data || []);
  };


  const getFilterSummary = () => {
    const filterDetails = [];

    // Date filter
    let dateFilterText = 'All Time';
    if (dateFilter !== 'all') {
      const dateRange = getDateRangeValues(dateFilter, customDateRange);
      if (dateRange) {
        dateFilterText = `${formatDisplayDate(dateRange.from)} to ${formatDisplayDate(dateRange.to)}`;
      } else if (dateFilter === 'custom' && customDateRange) {
        dateFilterText = `${formatDisplayDate(customDateRange.from)} to ${formatDisplayDate(customDateRange.to)}`;
      } else {
        const filterLabels = {
          'today': 'Today',
          'yesterday': 'Yesterday',
          'this-week': 'This Week',
          'last-week': 'Last Week',
          'last-7-days': 'Last 7 Days',
          'this-month': 'This Month',
          'last-month': 'Last Month',
          'last-30-days': 'Last 30 Days',
          'this-quarter': 'This Quarter',
          'last-quarter': 'Last Quarter',
          'last-90-days': 'Last 90 Days',
          'last-180-days': 'Last 180 Days',
          'this-year': 'This Year',
          'last-year': 'Last Year'
        };
        dateFilterText = filterLabels[dateFilter] || dateFilter;
      }
    }
    filterDetails.push({ label: 'Date Range', value: dateFilterText, applied: dateFilter !== 'all' });

    // Status filter (context-aware)
    let statusText = 'All Status';
    if (filters.status !== 'all') {
      if (activeTab === 'client') {
        statusText = filters.status === 'invested' ? 'Invested Clients' :
          filters.status === 'no-invested' ? 'No Investment Clients' :
            filters.status.charAt(0).toUpperCase() + filters.status.slice(1);
      } else {
        statusText = filters.status.charAt(0).toUpperCase() + filters.status.slice(1);
      }
    }

    // Only show status filter for investment report
    if (activeTab === 'investment' || activeTab === 'client') {
      filterDetails.push({ label: 'Status', value: statusText, applied: filters.status !== 'all' });
    }

    // Client filter (only for relevant tabs)
    if (activeTab === 'investment' || activeTab === 'transaction' || activeTab === 'commission' || activeTab === 'maturity') {
      let clientText = 'All Clients';
      if (filters.client !== 'all') {
        const selectedClient = allClients.find(c => c.id === filters.client);
        if (selectedClient) {
          clientText = `${selectedClient.first_name} ${selectedClient.last_name}`;
        }
      }
      filterDetails.push({ label: 'Client', value: clientText, applied: filters.client !== 'all' });
    }

    // Transaction type filter (only for transaction tab)
    if (activeTab === 'transaction') {
      const transactionText = filters.transactionType === 'all' ? 'All Types' : filters.transactionType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      filterDetails.push({ label: 'Transaction Type', value: transactionText, applied: filters.transactionType !== 'all' });
    }

    // Search term
    if (filters.searchTerm) {
      filterDetails.push({ label: 'Search', value: `"${filters.searchTerm}"`, applied: true });
    }

    return filterDetails;
  };

  const handleExportToPDF = async () => {
    try {
      // Fetch notification settings for header data
      const { data: notificationSettings, error: settingsError } = await supabase
        .from('notification_settings')
        .select('*')
        .limit(1)
        .single();

      if (settingsError) {
        console.error('Error fetching notification settings:', settingsError);
      }

      // Convert image URLs to base64 for PDF compatibility
      const convertImageToBase64 = async (imageUrl: string): Promise<string> => {
        try {
          if (!imageUrl) return '';

          // Handle relative URLs by making them absolute
          const fullUrl = imageUrl.startsWith('http') ? imageUrl : `${window.location.origin}${imageUrl}`;
          console.log('Converting image:', fullUrl);

          // Try fetch method first (works better for same-origin images)
          try {
            const response = await fetch(fullUrl);
            if (response.ok) {
              const blob = await response.blob();
              return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onloadend = () => {
                  const result = reader.result as string;
                  console.log('Image converted successfully via fetch:', result.substring(0, 50) + '...');
                  resolve(result);
                };
                reader.onerror = () => {
                  console.error('FileReader error');
                  resolve('');
                };
                reader.readAsDataURL(blob);
              });
            }
          } catch (fetchError) {
            console.log('Fetch failed, trying canvas method:', fetchError);
          }

          // Fallback to canvas method
          return new Promise((resolve) => {
            const img = new Image();

            img.onload = () => {
              try {
                console.log('Image loaded, converting to canvas:', img.width, 'x', img.height);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                if (!ctx) {
                  console.error('Could not get canvas context');
                  resolve('');
                  return;
                }

                canvas.width = img.width;
                canvas.height = img.height;

                ctx.drawImage(img, 0, 0);
                const dataURL = canvas.toDataURL('image/png', 0.9);
                console.log('Canvas conversion successful:', dataURL.substring(0, 50) + '...');
                resolve(dataURL);
              } catch (canvasError) {
                console.error('Canvas conversion error:', canvasError);
                resolve('');
              }
            };

            img.onerror = (error) => {
              console.error('Image loading error:', error, 'URL:', fullUrl);
              resolve('');
            };

            // Set crossOrigin before src for external images
            if (!fullUrl.includes(window.location.origin)) {
              img.crossOrigin = 'anonymous';
            }

            img.src = fullUrl;

            // Timeout after 10 seconds
            setTimeout(() => {
              console.error('Image loading timeout for:', fullUrl);
              resolve('');
            }, 10000);
          });
        } catch (error) {
          console.error('Error in convertImageToBase64:', error);
          return '';
        }
      };

      // Show loading toast
      toast({
        title: "Generating PDF",
        description: "Please wait while we prepare your report...",
      });

      // Convert logos to base64
      console.log('Starting logo conversion...');
      console.log('Post office logo URL:', notificationSettings?.post_office_logo_url);
      console.log('Admin logo URL:', notificationSettings?.admin_logo_url);

      const postOfficeLogoBase64 = await convertImageToBase64(
        notificationSettings?.post_office_logo_url || '/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png'
      );

      const adminLogoBase64 = await convertImageToBase64(
        notificationSettings?.admin_logo_url || '/lovable-uploads/e26f8b82-a2a2-46cc-a26e-fcf66ed47997.png'
      );

      console.log('Post office logo converted:', !!postOfficeLogoBase64);
      console.log('Admin logo converted:', !!adminLogoBase64);

      // If base64 conversion failed, try using the original URLs as fallback
      const postOfficeLogo = postOfficeLogoBase64 || (notificationSettings?.post_office_logo_url ?
        `${window.location.origin}${notificationSettings.post_office_logo_url}` :
        `${window.location.origin}/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png`);

      const adminLogo = adminLogoBase64 || (notificationSettings?.admin_logo_url ?
        `${window.location.origin}${notificationSettings.admin_logo_url}` :
        `${window.location.origin}/lovable-uploads/e26f8b82-a2a2-46cc-a26e-fcf66ed47997.png`);

      // Open a new window for the PDF report
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast({
          title: "Error",
          description: "Could not open print window. Please check your browser settings.",
          variant: "destructive",
        });
        return;
      }
      // Generate the report content based on active tab
      let title = '';
      let content = '';
      let details = '';
      const filterSummary = getFilterSummary();
      switch (activeTab) {
        case 'client': {
          title = 'Client Report';
          const totalClientsCount = clients.length;
          const investedClientsCount = clients.filter(c => c.total_investment! > 0).length;
          const totalInvestmentValue = clients.reduce((sum, c) => sum + (c.total_investment || 0), 0);
          const noInvestmentClientsCount = clients.filter(c => c.total_investment === 0).length;

          details = clients.map((client, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>${client.first_name} ${client.last_name}</td>
              <td>${client.email || client.mobile_number}</td>
              <td>${client.aadhar_number}</td>
              <td>${client.pan_card_number}</td>
              <td>${client.sb_account_number}</td>
              <td>${formatAmountInIndianFormat(client.total_investment || 0)}</td>
              <td><span class="status-badge status-active">Active</span></td>
              <td>${formatDisplayDate(client.created_at)}</td>
            </tr>
          `).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${totalClientsCount}</div>
                <div class="stat-label">Total Clients</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${investedClientsCount}</div>
                <div class="stat-label">Invested Clients</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalInvestmentValue)}</div>
                <div class="stat-label">Total Investment</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${noInvestmentClientsCount}</div>
                <div class="stat-label">No Investment</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Email/Phone</th>
                  <th>Aadhar</th>
                  <th>PAN</th>
                  <th>SB Account</th>
                  <th>Total Investment</th>
                  <th>Status</th>
                  <th>Created Date</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'investment': {
          title = 'Investment Report';
          const totalInvestmentsCount = investments.length;
          const totalInvestmentAmount = investments.reduce((sum, inv) => sum + inv.amount, 0);
          const totalMaturityAmountValue = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
          const activeInvestmentsCount = investments.filter(inv => inv.status === 'active').length;

          details = investments.map((investment, index) => {
            const primaryClient = investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A';
            const primaryPhone = investment.clients?.mobile_number || 'N/A';
            const primaryVillage = investment.clients?.village || 'N/A';
            const primaryCIF = investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A';

            const secondaryClient = investment.secondary_applicant ? `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = investment.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = investment.secondary_applicant?.village || 'N/A';
            const secondaryCIF = investment.secondary_applicant_cif_id || investment.secondary_applicant?.cif_id || 'N/A';

            return `
              <tr class="detail-item">
                <td>${index + 1}</td>
                <td>
                  <div><strong>Primary:</strong> ${primaryClient}</div>
                  <div><small>Phone: ${primaryPhone} | Village: ${primaryVillage}</small></div>
                  <div><small>CIF: ${primaryCIF}</small></div>
                  ${investment.secondary_applicant ? `
                    <div style="margin-top: 8px;"><strong>Secondary:</strong> ${secondaryClient}</div>
                    <div><small>Phone: ${secondaryPhone} | Village: ${secondaryVillage}</small></div>
                    <div><small>CIF: ${secondaryCIF}</small></div>
                  ` : ''}
                </td>
                <td>${investment.scheme_name}</td>
                <td>${formatAmountInIndianFormat(investment.amount)}</td>
                <td>${investment.interest_rate}%</td>
                <td>${formatDisplayDate(investment.investment_date)}</td>
                <td>${formatDisplayDate(investment.maturity_date)}</td>
                <td>${formatAmountInIndianFormat(investment.maturity_amount)}</td>
                <td><span class="status-badge status-${investment.status}">${investment.status}</span></td>
              </tr>
            `;
          }).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${totalInvestmentsCount}</div>
                <div class="stat-label">Total Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalInvestmentAmount)}</div>
                <div class="stat-label">Total Investment</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalMaturityAmountValue)}</div>
                <div class="stat-label">Total Maturity</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${activeInvestmentsCount}</div>
                <div class="stat-label">Active Investments</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Details</th>
                  <th>Scheme Name</th>
                  <th>Amount</th>
                  <th>Interest Rate</th>
                  <th>Investment Date</th>
                  <th>Maturity Date</th>
                  <th>Maturity Amount</th>
                  <th>Status</th>
                  <th>Remarks</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'transaction': {
          title = 'Transaction Report';
          const totalTransactionsCount = transactions.length;
          const totalTransactionAmountValue = transactions.reduce((sum, t) => sum + t.amount, 0);
          const investmentTransactionsCount = transactions.filter(t => t.amount_type === 'investment').length;
          const payoutTransactionsCount = transactions.filter(t => t.amount_type.includes('payout')).length;

          details = transactions.map((transaction, index) => {
            const primaryClient = transaction.investments?.clients ? `${transaction.investments.clients.first_name} ${transaction.investments.clients.last_name}` : 'N/A';
            const primaryPhone = transaction.investments?.clients?.mobile_number || 'N/A';
            const primaryVillage = transaction.investments?.clients?.village || 'N/A';
            const primaryCIF = transaction.investments?.primary_applicant_cif_id || transaction.investments?.clients?.cif_id || 'N/A';

            const secondaryClient = transaction.investments?.secondary_applicant ? `${transaction.investments.secondary_applicant.first_name} ${transaction.investments.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = transaction.investments?.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = transaction.investments?.secondary_applicant?.village || 'N/A';
            const secondaryCIF = transaction.investments?.secondary_applicant_cif_id || transaction.investments?.secondary_applicant?.cif_id || 'N/A';

            return `
              <tr class="detail-item">
                <td>${index + 1}</td>
                <td>
                  <div><strong>Primary:</strong> ${primaryClient}</div>
                  <div><small>Phone: ${primaryPhone} | Village: ${primaryVillage}</small></div>
                  <div><small>CIF: ${primaryCIF}</small></div>
                  ${transaction.investments?.secondary_applicant ? `
                    <div style="margin-top: 8px;"><strong>Secondary:</strong> ${secondaryClient}</div>
                    <div><small>Phone: ${secondaryPhone} | Village: ${secondaryVillage}</small></div>
                    <div><small>CIF: ${secondaryCIF}</small></div>
                  ` : ''}
                </td>
                <td>${transaction.investments?.scheme_name || 'N/A'}</td>
                <td>${formatDisplayDate(transaction.transaction_date)}</td>
                <td>${formatAmountInIndianFormat(transaction.amount)}</td>
                <td>${transaction.amount_type}</td>
                <td>${transaction.payment_mode || 'N/A'}</td>
              </tr>
            `;
          }).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${totalTransactionsCount}</div>
                <div class="stat-label">Total Transactions</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalTransactionAmountValue)}</div>
                <div class="stat-label">Total Amount</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${investmentTransactionsCount}</div>
                <div class="stat-label">Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${payoutTransactionsCount}</div>
                <div class="stat-label">Payouts</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Details</th>
                  <th>Scheme Name</th>
                  <th>Date</th>
                  <th>Amount</th>
                  <th>Type</th>
                  <th>Payment Mode</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'commission': {
          title = 'Commission Report';
          const totalCommissionAmount = investments.reduce((sum, inv) => sum + (inv.amount * inv.commission_percentage) / 100, 0);
          const totalTDSAmount = investments.reduce((sum, inv) => sum + inv.tds_amount, 0);
          const totalNetCommissionAmount = totalCommissionAmount - totalTDSAmount;
          const averageCommissionRate = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.commission_percentage, 0) / investments.length : 0;

          const details = investments.map((investment, index) => {
            const commissionAmount = (investment.amount * investment.commission_percentage) / 100;
            const netCommission = commissionAmount - investment.tds_amount;

            const primaryClient = investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A';
            const primaryPhone = investment.clients?.mobile_number || 'N/A';
            const primaryVillage = investment.clients?.village || 'N/A';
            const primaryCIF = investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A';

            const secondaryClient = investment.secondary_applicant ? `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = investment.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = investment.secondary_applicant?.village || 'N/A';
            const secondaryCIF = investment.secondary_applicant_cif_id || investment.secondary_applicant?.cif_id || 'N/A';

            return `
              <tr class="detail-item">
                <td>${index + 1}</td>
                <td>
                  <div><strong>Primary:</strong> ${primaryClient}</div>
                  <div><small>Phone: ${primaryPhone} | Village: ${primaryVillage}</small></div>
                  <div><small>CIF: ${primaryCIF}</small></div>
                  ${investment.secondary_applicant ? `
                    <div style="margin-top: 8px;"><strong>Secondary:</strong> ${secondaryClient}</div>
                    <div><small>Phone: ${secondaryPhone} | Village: ${secondaryVillage}</small></div>
                    <div><small>CIF: ${secondaryCIF}</small></div>
                  ` : ''}
                </td>
                <td>${investment.scheme_name}</td>
                <td>${formatAmountInIndianFormat(investment.amount)}</td>
                <td>${investment.commission_percentage}%</td>
                <td>${formatAmountInIndianFormat(commissionAmount)}</td>
                <td>${formatAmountInIndianFormat(investment.tds_amount)}</td>
                <td>${formatAmountInIndianFormat(netCommission)}</td>
                <td>${formatDisplayDate(investment.investment_date)}</td>
              </tr>
            `;
          }).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalCommissionAmount)}</div>
                <div class="stat-label">Total Commission</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalTDSAmount)}</div>
                <div class="stat-label">Total TDS</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalNetCommissionAmount)}</div>
                <div class="stat-label">Net Commission</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${averageCommissionRate.toFixed(1)}%</div>
                <div class="stat-label">Avg Commission Rate</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Details</th>
                  <th>Scheme Name</th>
                  <th>Investment Amount</th>
                  <th>Commission Rate</th>
                  <th>Commission Amount</th>
                  <th>TDS Amount</th>
                  <th>Net Commission</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'maturity': {
          title = 'Maturity Report';
          const isMaturitySoon = (maturityDate: string) => {
            const maturity = new Date(maturityDate);
            const today = new Date();
            const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            return daysUntilMaturity <= 30 && daysUntilMaturity > 0;
          };

          const getMaturityStatus = (investment: Investment) => {
            const maturity = new Date(investment.maturity_date);
            const today = new Date();
            const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            const daysSinceMaturity = Math.abs(daysUntilMaturity);

            if (investment.status === 'withdrawn') {
              return { status: 'Withdrawn', days: 'Withdrawn' };
            } else if (investment.status === 'reinvested') {
              return { status: 'Reinvested', days: 'Reinvested' };
            } else if (investment.status === 'matured' && daysUntilMaturity <= 0) {
              return { status: 'Overdue', days: `${daysSinceMaturity} days overdue` };
            } else if (investment.status === 'active' && daysUntilMaturity <= 0) {
              return { status: 'Overdue', days: `${daysSinceMaturity} days overdue` };
            } else if (daysUntilMaturity <= 30) {
              return { status: 'Maturing Soon', days: `${daysUntilMaturity} days left` };
            } else {
              return { status: 'Active', days: `${daysUntilMaturity} days left` };
            }
          };

          const maturingSoonCount = investments.filter(inv => isMaturitySoon(inv.maturity_date) && inv.status === 'active').length;
          const totalMaturityValueAmount = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
          const activeInvestmentCount = investments.filter(inv => inv.status === 'active').length;
          const maturedInvestmentCount = investments.filter(inv => inv.status === 'matured').length;
          const withdrawnInvestmentCount = investments.filter(inv => inv.status === 'withdrawn').length;
          const overdueCount = investments.filter(inv => {
            const maturity = new Date(inv.maturity_date);
            const today = new Date();
            return (inv.status === 'active' || inv.status === 'matured') && maturity < today;
          }).length;

          details = investments.map((investment, index) => {
            const maturityInfo = getMaturityStatus(investment);
            const isSoon = isMaturitySoon(investment.maturity_date) && investment.status === 'active';
            const isOverdue = (investment.status === 'active' || investment.status === 'matured') && new Date(investment.maturity_date) < new Date();
            const rowClass = isSoon ? 'maturity-soon' : isOverdue ? 'maturity-overdue' : '';

            const primaryClient = investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A';
            const primaryPhone = investment.clients?.mobile_number || 'N/A';
            const primaryVillage = investment.clients?.village || 'N/A';
            const primaryCIF = investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A';

            const secondaryClient = investment.secondary_applicant ? `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = investment.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = investment.secondary_applicant?.village || 'N/A';
            const secondaryCIF = investment.secondary_applicant_cif_id || investment.secondary_applicant?.cif_id || 'N/A';

            return `
              <tr class="detail-item ${rowClass}">
                <td>${index + 1}</td>
                <td>
                  <div><strong>Primary:</strong> ${primaryClient}</div>
                  <div><small>Phone: ${primaryPhone} | Village: ${primaryVillage}</small></div>
                  <div><small>CIF: ${primaryCIF}</small></div>
                  ${investment.secondary_applicant ? `
                    <div style="margin-top: 8px;"><strong>Secondary:</strong> ${secondaryClient}</div>
                    <div><small>Phone: ${secondaryPhone} | Village: ${secondaryVillage}</small></div>
                    <div><small>CIF: ${secondaryCIF}</small></div>
                  ` : ''}
                </td>
                <td>${investment.scheme_name}</td>
                <td>${formatAmountInIndianFormat(investment.amount)}</td>
                <td>${formatDisplayDate(investment.maturity_date)}</td>
                <td>${formatAmountInIndianFormat(investment.maturity_amount)}</td>
                <td>${maturityInfo.days}</td>
                <td><span class="status-badge status-${investment.status}">${maturityInfo.status}</span></td>
                <td>${investment.remark || 'No remarks'}</td>
              </tr>
            `;
          }).join('');

          content = `
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${activeInvestmentCount}</div>
                <div class="stat-label">Active Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${maturedInvestmentCount}</div>
                <div class="stat-label">Matured Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${maturingSoonCount}</div>
                <div class="stat-label">Maturing Soon (30 days)</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${overdueCount}</div>
                <div class="stat-label">Overdue Maturity</div>
              </div>
            </div>
            <table class="report-table">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Details</th>
                  <th>Scheme Name</th>
                  <th>Amount</th>
                  <th>Maturity Date</th>
                  <th>Maturity Amount</th>
                  <th>Maturity Status</th>
                  <th>Current Status</th>
                  <th>Remarks</th>
                </tr>
              </thead>
              <tbody>
                ${details}
              </tbody>
            </table>
          `;
          break;
        }
        case 'client_wise': {
          const { client, investments, transactions, nominees, totalInvestment, totalMaturity } = selectedClientData;

          if (!client) {
            title = 'Client-Wise Report';
            content = `
              <div class="text-center" style="padding: 40px;">
                <h3>No Client Selected</h3>
                <p>Please select a client to generate the report.</p>
              </div>
            `;
            break;
          }

          title = `Client-Wise Report - ${client.first_name} ${client.last_name}`;
          const totalProfit = totalMaturity - totalInvestment;
          const activeInvestments = investments.filter(inv => inv.status === 'active').length;
          const maturedInvestments = investments.filter(inv => inv.status === 'matured').length;
          const primaryInvestments = investments.filter(inv => inv.applicant_type === 'primary').length;
          const secondaryInvestments = investments.filter(inv => inv.applicant_type === 'secondary').length;

          // Client details section
          const clientDetails = `
            <div class="client-profile">
              <h3>Client Profile</h3>
              <div class="client-info-grid">
                <div class="info-section">
                  <h4>Personal Information</h4>
                  <p><strong>Name:</strong> ${client.first_name} ${client.last_name}</p>
                  <p><strong>Mobile:</strong> ${client.mobile_number}</p>
                  <p><strong>Email:</strong> ${client.email || 'N/A'}</p>
                  <p><strong>Village:</strong> ${client.village || 'N/A'}</p>
                </div>
                <div class="info-section">
                  <h4>Identity Information</h4>
                  <p><strong>CIF ID:</strong> ${client.cif_id || 'N/A'}</p>
                  <p><strong>PAN Card:</strong> ${client.pan_card_number || 'N/A'}</p>
                  <p><strong>Aadhar Number:</strong> ${client.aadhar_number || 'N/A'}</p>
                </div>
                <div class="info-section">
                  <h4>Address</h4>
                  <p><strong>Address:</strong> ${client.address || 'N/A'}</p>
                  <p><strong>City:</strong> ${client.city || 'N/A'}</p>
                  <p><strong>State:</strong> ${client.state || 'N/A'}</p>
                  <p><strong>Pincode:</strong> ${client.pincode || 'N/A'}</p>
                </div>
              </div>
            </div>
          `;

          // Investment details
          const investmentDetails = investments.map((investment, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>
                <div><strong>${investment.scheme_name}</strong></div>
                <div style="font-size: 10px; color: #666;">${investment.scheme_code}</div>
              </td>
              <td>
                <span class="applicant-badge ${investment.applicant_type === 'primary' ? 'primary' : 'secondary'}">
                  ${investment.applicant_type === 'primary' ? 'Primary' : 'Secondary'}
                </span>
              </td>
              <td>${formatAmountInIndianFormat(investment.amount)}</td>
              <td>${investment.interest_rate}%</td>
              <td>${formatDisplayDate(investment.investment_date)}</td>
              <td>${formatDisplayDate(investment.maturity_date)}</td>
              <td>${formatAmountInIndianFormat(investment.maturity_amount)}</td>
              <td><span class="status-badge status-${investment.status}">${investment.status}</span></td>
              <td>${investment.remark || 'No remarks'}</td>
            </tr>
          `).join('');

          // Transaction details
          const transactionDetails = transactions.slice(0, 20).map((transaction, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>${formatDisplayDate(transaction.transaction_date)}</td>
              <td>
                <div><strong>${transaction.investments?.scheme_name || 'N/A'}</strong></div>
                <div style="font-size: 10px; color: #666;">${transaction.investments?.scheme_code || 'N/A'}</div>
              </td>
              <td>
                <span class="transaction-badge transaction-${transaction.amount_type}">
                  ${transaction.amount_type.replace(/_/g, ' ')}
                </span>
              </td>
              <td>${formatAmountInIndianFormat(transaction.amount)}</td>
              <td>${transaction.payment_mode || 'N/A'}</td>
              <td>${transaction.reference_number || 'N/A'}</td>
            </tr>
          `).join('');

          // Nominee details
          const nomineeDetails = nominees.map((nominee, index) => `
            <tr class="detail-item">
              <td>${index + 1}</td>
              <td>${nominee.name}</td>
              <td>${nominee.relation}</td>
              <td>${nominee.birthdate ? formatDisplayDate(nominee.birthdate) : 'N/A'}</td>
            </tr>
          `).join('');

          content = `
            ${clientDetails}

            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-value">${investments.length}</div>
                <div class="stat-label">Total Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalInvestment)}</div>
                <div class="stat-label">Total Investment</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalMaturity)}</div>
                <div class="stat-label">Total Maturity</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${formatAmountInIndianFormat(totalProfit)}</div>
                <div class="stat-label">Total Profit</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${primaryInvestments}</div>
                <div class="stat-label">Primary Investments</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">${secondaryInvestments}</div>
                <div class="stat-label">Secondary Investments</div>
              </div>
            </div>

            ${investments.length > 0 ? `
              <h3>Investments (${investments.length})</h3>
              <table class="report-table">
                <thead>
                  <tr>
                    <th>S.No</th>
                    <th>Scheme</th>
                    <th>Applicant Type</th>
                    <th>Amount</th>
                    <th>Interest Rate</th>
                    <th>Investment Date</th>
                    <th>Maturity Date</th>
                    <th>Maturity Amount</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  ${investmentDetails}
                </tbody>
              </table>
            ` : '<p>No investments found</p>'}

            ${nominees.length > 0 ? `
              <h3>Nominees (${nominees.length})</h3>
              <table class="report-table">
                <thead>
                  <tr>
                    <th>S.No</th>
                    <th>Name</th>
                    <th>Relation</th>
                    <th>Birthdate</th>
                  </tr>
                </thead>
                <tbody>
                  ${nomineeDetails}
                </tbody>
              </table>
            ` : ''}

            ${transactions.length > 0 ? `
              <h3>Recent Transactions (${Math.min(transactions.length, 20)})</h3>
              <table class="report-table">
                <thead>
                  <tr>
                    <th>S.No</th>
                    <th>Date</th>
                    <th>Scheme</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Payment Mode</th>
                    <th>Reference</th>
                  </tr>
                </thead>
                <tbody>
                  ${transactionDetails}
                </tbody>
              </table>
            ` : ''}
          `;
          break;
        }
      }
      
      // Use the PDF export utility
      await exportToPDF({
        title,
        content,
        filterSummary
      });

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error",
        description: "Failed to generate PDF report",
        variant: "destructive",
      });
    }
  };

  const exportToExcel = () => {
    try {
      let csvContent = '';
      let headers = '';

      switch (activeTab) {
        case 'client':
          headers = 'Client Name,Email/Phone,Aadhar,PAN,SB Account,Total Investment,Status,Created Date\n';
          csvContent = clients.map(client =>
            `"${client.first_name} ${client.last_name}","${client.email || client.mobile_number}","${client.aadhar_number}","${client.pan_card_number}","${client.sb_account_number}","${formatAmountInIndianFormat(client.total_investment || 0)}","Active","${formatDisplayDate(client.created_at)}"`
          ).join('\n');
          break;
        case 'investment':
          headers = 'Primary Client,Primary Phone,Primary Village,Primary CIF,Secondary Client,Secondary Phone,Secondary Village,Secondary CIF,Scheme Name,Invested Amount,Interest Rate,Investment Date,Maturity Date,Maturity Amount,Status\n';
          csvContent = investments.map(investment => {
            const primaryClient = investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A';
            const primaryPhone = investment.clients?.mobile_number || 'N/A';
            const primaryVillage = investment.clients?.village || 'N/A';
            const primaryCIF = investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A';

            const secondaryClient = investment.secondary_applicant ? `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = investment.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = investment.secondary_applicant?.village || 'N/A';
            const secondaryCIF = investment.secondary_applicant_cif_id || investment.secondary_applicant?.cif_id || 'N/A';

            return `"${primaryClient}","${primaryPhone}","${primaryVillage}","${primaryCIF}","${secondaryClient}","${secondaryPhone}","${secondaryVillage}","${secondaryCIF}","${investment.scheme_name}","${formatAmountInIndianFormat(investment.amount)}","${investment.interest_rate}%","${formatDisplayDate(investment.investment_date)}","${formatDisplayDate(investment.maturity_date)}","${formatAmountInIndianFormat(investment.maturity_amount)}","${investment.status}"`;
          }).join('\n');
          break;
        case 'transaction':
          headers = 'Primary Client,Primary Phone,Primary Village,Primary CIF,Secondary Client,Secondary Phone,Secondary Village,Secondary CIF,Scheme,Transaction Date,Amount,Type,Payment Mode\n';
          csvContent = transactions.map(transaction => {
            const primaryClient = transaction.investments?.clients ? `${transaction.investments.clients.first_name} ${transaction.investments.clients.last_name}` : 'N/A';
            const primaryPhone = transaction.investments?.clients?.mobile_number || 'N/A';
            const primaryVillage = transaction.investments?.clients?.village || 'N/A';
            const primaryCIF = transaction.investments?.primary_applicant_cif_id || transaction.investments?.clients?.cif_id || 'N/A';

            const secondaryClient = transaction.investments?.secondary_applicant ? `${transaction.investments.secondary_applicant.first_name} ${transaction.investments.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = transaction.investments?.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = transaction.investments?.secondary_applicant?.village || 'N/A';
            const secondaryCIF = transaction.investments?.secondary_applicant_cif_id || transaction.investments?.secondary_applicant?.cif_id || 'N/A';

            return `"${primaryClient}","${primaryPhone}","${primaryVillage}","${primaryCIF}","${secondaryClient}","${secondaryPhone}","${secondaryVillage}","${secondaryCIF}","${transaction.investments?.scheme_name || 'N/A'}","${formatDisplayDate(transaction.transaction_date)}","${formatAmountInIndianFormat(transaction.amount)}","${transaction.amount_type}","${transaction.payment_mode || 'N/A'}"`;
          }).join('\n');
          break;
        case 'commission':
          headers = 'Primary Client,Primary Phone,Primary Village,Primary CIF,Secondary Client,Secondary Phone,Secondary Village,Secondary CIF,Scheme,Investment Amount,Commission Amount,TDS,Net Commission,Investment Date\n';
          csvContent = investments.map(investment => {
            const commissionAmount = (investment.amount * investment.commission_percentage) / 100;
            const netCommission = commissionAmount - investment.tds_amount;

            const primaryClient = investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A';
            const primaryPhone = investment.clients?.mobile_number || 'N/A';
            const primaryVillage = investment.clients?.village || 'N/A';
            const primaryCIF = investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A';

            const secondaryClient = investment.secondary_applicant ? `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = investment.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = investment.secondary_applicant?.village || 'N/A';
            const secondaryCIF = investment.secondary_applicant_cif_id || investment.secondary_applicant?.cif_id || 'N/A';

            return `"${primaryClient}","${primaryPhone}","${primaryVillage}","${primaryCIF}","${secondaryClient}","${secondaryPhone}","${secondaryVillage}","${secondaryCIF}","${investment.scheme_name}","₹${investment.amount.toLocaleString()}","₹${commissionAmount.toLocaleString()}","₹${investment.tds_amount.toLocaleString()}","₹${netCommission.toLocaleString()}","${formatDisplayDate(investment.investment_date)}"`;
          }).join('\n');
          break;
        case 'maturity':
          headers = 'Primary Client,Primary Phone,Primary Village,Primary CIF,Secondary Client,Secondary Phone,Secondary Village,Secondary CIF,Scheme,Invested Amount,Maturity Date,Maturity Amount,Maturity Status,Current Status\n';
          csvContent = investments.map(investment => {
            const maturity = new Date(investment.maturity_date);
            const today = new Date();
            const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

            const primaryClient = investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A';
            const primaryPhone = investment.clients?.mobile_number || 'N/A';
            const primaryVillage = investment.clients?.village || 'N/A';
            const primaryCIF = investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A';

            const secondaryClient = investment.secondary_applicant ? `${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}` : 'N/A';
            const secondaryPhone = investment.secondary_applicant?.mobile_number || 'N/A';
            const secondaryVillage = investment.secondary_applicant?.village || 'N/A';
            const secondaryCIF = investment.secondary_applicant_cif_id || investment.secondary_applicant?.cif_id || 'N/A';

            let maturityStatus = '';
            if (investment.status === 'withdrawn') {
              maturityStatus = 'Withdrawn';
            } else if (investment.status === 'reinvested') {
              maturityStatus = 'Reinvested';
            } else if (investment.status === 'matured' && daysUntilMaturity <= 0) {
              maturityStatus = `${Math.abs(daysUntilMaturity)} days overdue`;
            } else if (investment.status === 'active' && daysUntilMaturity <= 0) {
              maturityStatus = `${Math.abs(daysUntilMaturity)} days overdue`;
            } else if (daysUntilMaturity <= 30) {
              maturityStatus = `${daysUntilMaturity} days left (Soon)`;
            } else {
              maturityStatus = `${daysUntilMaturity} days left`;
            }

            return `"${primaryClient}","${primaryPhone}","${primaryVillage}","${primaryCIF}","${secondaryClient}","${secondaryPhone}","${secondaryVillage}","${secondaryCIF}","${investment.scheme_name}","₹${investment.amount.toLocaleString()}","${formatDisplayDate(investment.maturity_date)}","₹${investment.maturity_amount.toLocaleString()}","${maturityStatus}","${investment.status}"`;
          }).join('\n');
          break;
        case 'client_wise': {
          const { client, investments, transactions, nominees } = selectedClientData;

          if (!client) {
            toast({
              title: "Error",
              description: "Please select a client to export data",
              variant: "destructive",
            });
            return;
          }

          // Client Information
          let clientInfo = `Client Information\n`;
          clientInfo += `Name,"${client.first_name} ${client.last_name}"\n`;
          clientInfo += `Mobile,"${client.mobile_number}"\n`;
          clientInfo += `Email,"${client.email || 'N/A'}"\n`;
          clientInfo += `Village,"${client.village || 'N/A'}"\n`;
          clientInfo += `CIF ID,"${client.cif_id || 'N/A'}"\n`;
          clientInfo += `PAN Card,"${client.pan_card_number || 'N/A'}"\n`;
          clientInfo += `Aadhar Number,"${client.aadhar_number || 'N/A'}"\n`;
          clientInfo += `Address,"${client.address || 'N/A'}"\n`;
          clientInfo += `City,"${client.city || 'N/A'}"\n`;
          clientInfo += `State,"${client.state || 'N/A'}"\n`;
          clientInfo += `Pincode,"${client.pincode || 'N/A'}"\n\n`;

          // Investment Summary
          const totalInvestment = investments.reduce((sum, inv) => sum + inv.amount, 0);
          const totalMaturity = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
          const totalProfit = totalMaturity - totalInvestment;
          const primaryInvestments = investments.filter(inv => inv.applicant_type === 'primary').length;
          const secondaryInvestments = investments.filter(inv => inv.applicant_type === 'secondary').length;

          let summary = `Investment Summary\n`;
          summary += `Total Investments,"${investments.length}"\n`;
          summary += `Total Investment Amount,"${formatAmountInIndianFormat(totalInvestment)}"\n`;
          summary += `Total Maturity Amount,"${formatAmountInIndianFormat(totalMaturity)}"\n`;
          summary += `Total Profit,"${formatAmountInIndianFormat(totalProfit)}"\n`;
          summary += `Primary Investments,"${primaryInvestments}"\n`;
          summary += `Secondary Investments,"${secondaryInvestments}"\n\n`;

          // Investments
          let investmentHeaders = `Investments\n`;
          investmentHeaders += `S.No,Scheme Name,Scheme Code,Applicant Type,Amount,Interest Rate,Investment Date,Maturity Date,Maturity Amount,Status\n`;

          const investmentData = investments.map((investment, index) =>
            `"${index + 1}","${investment.scheme_name}","${investment.scheme_code}","${investment.applicant_type === 'primary' ? 'Primary' : 'Secondary'}","${formatAmountInIndianFormat(investment.amount)}","${investment.interest_rate}%","${formatDisplayDate(investment.investment_date)}","${formatDisplayDate(investment.maturity_date)}","${formatAmountInIndianFormat(investment.maturity_amount)}","${investment.status}"`
          ).join('\n');

          // Nominees
          let nomineeHeaders = `\n\nNominees\n`;
          nomineeHeaders += `S.No,Name,Relation,Birthdate\n`;

          const nomineeData = nominees.map((nominee, index) =>
            `"${index + 1}","${nominee.name}","${nominee.relation}","${nominee.birthdate ? formatDisplayDate(nominee.birthdate) : 'N/A'}"`
          ).join('\n');

          // Recent Transactions
          let transactionHeaders = `\n\nRecent Transactions\n`;
          transactionHeaders += `S.No,Date,Scheme,Type,Amount,Payment Mode,Reference\n`;

          const transactionData = transactions.slice(0, 50).map((transaction, index) =>
            `"${index + 1}","${formatDisplayDate(transaction.transaction_date)}","${transaction.investments?.scheme_name || 'N/A'}","${transaction.amount_type.replace(/_/g, ' ')}","${formatAmountInIndianFormat(transaction.amount)}","${transaction.payment_mode || 'N/A'}","${transaction.reference_number || 'N/A'}"`
          ).join('\n');

          csvContent = clientInfo + summary + investmentHeaders + investmentData + nomineeHeaders + nomineeData + transactionHeaders + transactionData;
          break;
        }
      }

      const element = document.createElement('a');
      const file = new Blob([headers + csvContent], { type: 'text/csv' });
      element.href = URL.createObjectURL(file);
      element.download = `${activeTab}_report_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);

      toast({
        title: "Success",
        description: "Excel report exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export Excel report",
        variant: "destructive",
      });
    }
  };

  const resetFilters = () => {
    setFilters({
      searchTerm: '',
      dateFrom: '',
      dateTo: '',
      status: 'all',
      scheme: 'all',
      client: 'all',
      amountFrom: '',
      amountTo: '',
      transactionType: 'all',
    });
    setDateFilter('all');
    setCustomDateRange(undefined);
  };


  const getStatusBadge = (status: string, type: 'client' | 'investment' = 'investment') => {
    if (type === 'client') {
      return status === 'invested' ? (
        <Badge variant="default" className="bg-green-100 text-green-800">Invested</Badge>
      ) : (
        <Badge variant="secondary">No Investment</Badge>
      );
    }

    const variants = {
      active: <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>,
      matured: <Badge variant="default" className="bg-blue-100 text-blue-800">Matured</Badge>,
      withdrawn: <Badge variant="secondary">Withdrawn</Badge>,
      reinvested: <Badge variant="default" className="bg-purple-100 text-purple-800">Reinvested</Badge>
    };
    return variants[status as keyof typeof variants] || <Badge variant="secondary">{status}</Badge>;
  };

  const renderClientReport = () => (
    <div className="space-y-4">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{clients.length}</p>
                <p className="text-sm text-gray-600">Total Clients</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{clients.filter(c => c.total_investment! > 0).length}</p>
                <p className="text-sm text-gray-600">Invested Clients</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <IndianRupee className="h-8 w-8 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">{formatAmountInIndianFormat(clients.reduce((sum, c) => sum + (c.total_investment || 0), 0))}</p>
                <p className="text-sm text-gray-600">Total Investment</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{clients.filter(c => c.total_investment === 0).length}</p>
                <p className="text-sm text-gray-600">No Investment</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="font-semibold w-16">S.No</TableHead>
              <TableHead className="font-semibold">Client Name</TableHead>
              <TableHead className="font-semibold">Contact</TableHead>
              <TableHead className="font-semibold">Aadhar Number</TableHead>
              <TableHead className="font-semibold">PAN Card</TableHead>
              <TableHead className="font-semibold text-right">Total Investment</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Join Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clients.map((client: Client, index: number) => (
              <TableRow key={client.id} className="hover:bg-gray-50">
                <TableCell className="font-medium text-center">
                  <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                </TableCell>
                <TableCell className="font-medium">
                  <div>
                    <p className="font-semibold">{client.first_name} {client.last_name}</p>
                    <p className="text-sm text-gray-500">{client.email}</p>
                  </div>
                </TableCell>
                <TableCell>{client.mobile_number || 'N/A'}</TableCell>
                <TableCell>
                  <span className="font-mono text-sm">{client.aadhar_number || 'N/A'}</span>
                </TableCell>
                <TableCell>
                  <span className="font-mono text-sm">{client.pan_card_number || 'N/A'}</span>
                </TableCell>
                <TableCell className="text-right">
                  <span className="font-bold text-green-600">{formatAmountInIndianFormat(client.total_investment || 0)}</span>
                </TableCell>
                <TableCell>
                  {getStatusBadge(client.total_investment! > 0 ? 'invested' : 'no-investment', 'client')}
                </TableCell>
                <TableCell>
                  <span className="text-sm">{formatDisplayDate(client.created_at)}</span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );


  const renderInvestmentReport = () => {
    const totalInvestment = investments.reduce((sum, inv) => sum + inv.amount, 0);
    const totalMaturityAmount = investments.reduce((sum, inv) => sum + inv.maturity_amount, 0);
    const activeInvestments = investments.filter(inv => inv.status === 'active').length;

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{investments.length}</p>
                  <p className="text-sm text-gray-600">Total Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalInvestment)}</p>
                  <p className="text-sm text-gray-600">Total Investment</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalMaturityAmount)}</p>
                  <p className="text-sm text-gray-600">Total Maturity</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{activeInvestments}</p>
                  <p className="text-sm text-gray-600">Active Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Details</TableHead>
                <TableHead className="font-semibold">Village</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold text-right">Investment Amount</TableHead>
                <TableHead className="font-semibold text-center">Interest Rate</TableHead>
                <TableHead className="font-semibold text-right">Maturity Amount</TableHead>
                <TableHead className="font-semibold">Investment Date</TableHead>
                <TableHead className="font-semibold">Maturity Date</TableHead>
                <TableHead className="font-semibold">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment, index: number) => (
                <TableRow key={investment.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium text-center">
                    <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                  </TableCell>
                  <TableCell className="font-medium">
                    <div>
                      <div className="mb-2">
                        <p className="font-semibold">
                          <span className="text-blue-600">Primary:</span> {investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}
                        </p>
                        <p className="text-xs text-gray-500">
                          Phone: {investment.clients?.mobile_number || 'N/A'} | CIF: {investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A'}
                        </p>
                      </div>

                      {investment.secondary_applicant && (
                        <div>
                          <p className="font-semibold">
                            <span className="text-purple-600">Secondary:</span> {`${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}`}
                          </p>
                          <p className="text-xs text-gray-500">
                            Phone: {investment.secondary_applicant.mobile_number || 'N/A'} | CIF: {investment.secondary_applicant_cif_id || investment.secondary_applicant.cif_id || 'N/A'}
                          </p>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{investment.clients?.village || 'N/A'}</p>
                      {investment.secondary_applicant && (
                        <p className="text-sm text-gray-500 mt-2">
                          <span className="text-purple-600">Secondary:</span> {investment.secondary_applicant.village || 'N/A'}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{investment.scheme_name}</p>
                      <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                      <p className="text-xs text-gray-500">{investment.lock_in_period_months} months lock-in</p>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-blue-600">{formatAmountInIndianFormat(investment.amount)}</span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="font-semibold">{investment.interest_rate}%</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-green-600">{formatAmountInIndianFormat(investment.maturity_amount)}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{formatDisplayDate(investment.investment_date)}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{formatDisplayDate(investment.maturity_date)}</span>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(investment.status)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  const renderTransactionReport = () => {
    const totalTransactionAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const investmentTransactions = transactions.filter(t => t.amount_type === 'investment').length;
    const payoutTransactions = transactions.filter(t => t.amount_type.includes('payout')).length;
    const otherTransactions = transactions.filter(t => !['investment', 'interest_payout', 'maturity_payout'].includes(t.amount_type)).length;

    const getTransactionTypeBadge = (type: string) => {
      const typeMap = {
        investment: <Badge className="bg-blue-100 text-blue-800">Investment</Badge>,
        interest_payout: <Badge className="bg-green-100 text-green-800">Interest Payout</Badge>,
        maturity_payout: <Badge className="bg-purple-100 text-purple-800">Maturity Payout</Badge>,
        penalty: <Badge variant="destructive">Penalty</Badge>,
        other: <Badge variant="secondary">Other</Badge>
      };
      return typeMap[type as keyof typeof typeMap] || <Badge variant="outline">{type}</Badge>;
    };

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{transactions.length}</p>
                  <p className="text-sm text-gray-600">Total Transactions</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalTransactionAmount)}</p>
                  <p className="text-sm text-gray-600">Total Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{investmentTransactions}</p>
                  <p className="text-sm text-gray-600">Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{payoutTransactions}</p>
                  <p className="text-sm text-gray-600">Payouts</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Details</TableHead>
                <TableHead className="font-semibold">Village</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold">Transaction Date</TableHead>
                <TableHead className="font-semibold text-right">Amount</TableHead>
                <TableHead className="font-semibold">Type</TableHead>
                <TableHead className="font-semibold">Payment Mode</TableHead>
                <TableHead className="font-semibold">Remark</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction, index: number) => (
                <TableRow key={transaction.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium text-center">
                    <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                  </TableCell>
                  <TableCell className="font-medium">
                    <div>
                      <div className="mb-2">
                        <p className="font-semibold">
                          <span className="text-blue-600">Primary:</span> {transaction.investments?.clients ?
                            `${transaction.investments.clients.first_name} ${transaction.investments.clients.last_name}` : 'N/A'}
                        </p>
                        <p className="text-xs text-gray-500">
                          Phone: {transaction.investments?.clients?.mobile_number || 'N/A'} |
                          CIF: {transaction.investments?.primary_applicant_cif_id || transaction.investments?.clients?.cif_id || 'N/A'}
                        </p>
                      </div>

                      {transaction.investments?.secondary_applicant && (
                        <div>
                          <p className="font-semibold">
                            <span className="text-purple-600">Secondary:</span> {
                              `${transaction.investments.secondary_applicant.first_name} ${transaction.investments.secondary_applicant.last_name}`
                            }
                          </p>
                          <p className="text-xs text-gray-500">
                            Phone: {transaction.investments.secondary_applicant.mobile_number || 'N/A'} |
                            CIF: {transaction.investments?.secondary_applicant_cif_id || transaction.investments.secondary_applicant.cif_id || 'N/A'}
                          </p>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{transaction.investments?.clients?.village || 'N/A'}</p>
                      {transaction.investments?.secondary_applicant && (
                        <p className="text-sm text-gray-500 mt-2">
                          <span className="text-purple-600">Secondary:</span> {transaction.investments.secondary_applicant.village || 'N/A'}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{transaction.investments?.scheme_name || 'N/A'}</p>
                      <p className="text-sm text-gray-500">{transaction.investments?.scheme_code || 'N/A'}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{formatDisplayDate(transaction.transaction_date)}</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-bold text-green-600">{formatAmountInIndianFormat(transaction.amount)}</span>
                  </TableCell>
                  <TableCell>
                    {getTransactionTypeBadge(transaction.amount_type)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="font-medium">
                      {transaction.payment_mode || 'N/A'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{transaction.remark || '-'}</span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  const renderCommissionReport = () => {
    const totalCommission = investments.reduce((sum, inv) => sum + (inv.amount * inv.commission_percentage) / 100, 0);
    const totalTDS = investments.reduce((sum, inv) => sum + inv.tds_amount, 0);
    const totalNetCommission = totalCommission - totalTDS;
    const averageCommissionRate = investments.length > 0 ? investments.reduce((sum, inv) => sum + inv.commission_percentage, 0) / investments.length : 0;

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalCommission)}</p>
                  <p className="text-sm text-gray-600">Total Commission</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalTDS)}</p>
                  <p className="text-sm text-gray-600">Total TDS</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalNetCommission)}</p>
                  <p className="text-sm text-gray-600">Net Commission</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{averageCommissionRate.toFixed(1)}%</p>
                  <p className="text-sm text-gray-600">Avg Commission</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Details</TableHead>
                <TableHead className="font-semibold">Village</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold text-right">Investment Amount</TableHead>
                <TableHead className="font-semibold text-center">Commission Rate</TableHead>
                <TableHead className="font-semibold text-right">Commission Amount</TableHead>
                <TableHead className="font-semibold text-right">TDS Amount</TableHead>
                <TableHead className="font-semibold text-right">Net Commission</TableHead>
                <TableHead className="font-semibold">Investment Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment, index: number) => {
                const commissionAmount = (investment.amount * investment.commission_percentage) / 100;
                return (
                  <TableRow key={investment.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium text-center">
                      <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                    </TableCell>
                    <TableCell className="font-medium">
                      <div>
                        <div className="mb-2">
                          <p className="font-semibold">
                            <span className="text-blue-600">Primary:</span> {investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}
                          </p>
                          <p className="text-xs text-gray-500">
                            Phone: {investment.clients?.mobile_number || 'N/A'} | CIF: {investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A'}
                          </p>
                        </div>

                        {investment.secondary_applicant && (
                          <div>
                            <p className="font-semibold">
                              <span className="text-purple-600">Secondary:</span> {`${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}`}
                            </p>
                            <p className="text-xs text-gray-500">
                              Phone: {investment.secondary_applicant.mobile_number || 'N/A'} | CIF: {investment.secondary_applicant_cif_id || investment.secondary_applicant.cif_id || 'N/A'}
                            </p>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.clients?.village || 'N/A'}</p>
                        {investment.secondary_applicant && (
                          <p className="text-sm text-gray-500 mt-2">
                            <span className="text-purple-600">Secondary:</span> {investment.secondary_applicant.village || 'N/A'}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.scheme_name}</p>
                        <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                        <p className="text-xs text-gray-500">{investment.lock_in_period_months} months</p>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-blue-600">{formatAmountInIndianFormat(investment.amount)}</span>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline" className="font-semibold text-yellow-700">{investment.commission_percentage}%</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-green-600">{formatAmountInIndianFormat(commissionAmount)}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-red-600">{formatAmountInIndianFormat(investment.tds_amount)}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-purple-600">{formatAmountInIndianFormat((investment.amount * investment.commission_percentage) / 100 - investment.tds_amount)}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{formatDisplayDate(investment.investment_date)}</span>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  const renderClientWiseReport = () => {
    const { client, investments, transactions, nominees, totalInvestment, totalMaturity } = selectedClientData;

    if (!client) {
      return (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium">Please select a client to view detailed report</h3>
          <p className="text-gray-500 mt-2">Use the client filter above to select a specific client</p>

          <div className="mt-6 max-w-md mx-auto">
            <Label className="text-left block mb-2">Select Client</Label>
            <Select
              value={filters.client}
              onValueChange={(value) => {
                setFilters({ ...filters, client: value });
                fetchClientWiseReport();
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a client" />
              </SelectTrigger>
              <SelectContent>
                {allClients.map((client) => (
                  <SelectItem key={client.id} value={client.id}>
                    {client.first_name} {client.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      );
    }

    const activeInvestments = investments.filter(inv => inv.status === 'active').length;
    const maturedInvestments = investments.filter(inv => inv.status === 'matured').length;
    const totalProfit = totalMaturity - totalInvestment;

    return (
      <div className="space-y-6">
        {/* Client Profile Card */}
        <Card>
          <CardHeader>
            <CardTitle>Client Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Personal Information</h4>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">Name:</span>
                    <p className="font-medium">{client.first_name} {client.last_name}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Mobile:</span>
                    <p className="font-medium">{client.mobile_number}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Email:</span>
                    <p className="font-medium">{client.email || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Village:</span>
                    <p className="font-medium">{client.village || 'N/A'}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Identity Information</h4>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">CIF ID:</span>
                    <p className="font-medium">{client.cif_id || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">PAN Card:</span>
                    <p className="font-medium">{client.pan_card_number || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Aadhar Number:</span>
                    <p className="font-medium">{client.aadhar_number || 'N/A'}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Address</h4>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-500">Address:</span>
                    <p className="font-medium">{client.address || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">City:</span>
                    <p className="font-medium">{client.city || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">State:</span>
                    <p className="font-medium">{client.state || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Pincode:</span>
                    <p className="font-medium">{client.pincode || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Investment Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{investments.length}</p>
                  <p className="text-sm text-gray-600">Total Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalInvestment)}</p>
                  <p className="text-sm text-gray-600">Total Investment</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalMaturity)}</p>
                  <p className="text-sm text-gray-600">Total Maturity</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-yellow-600" />
                <div>
                  <p className="text-2xl font-bold">{formatAmountInIndianFormat(totalProfit)}</p>
                  <p className="text-sm text-gray-600">Total Profit</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Nominees */}
        <Card>
          <CardHeader>
            <CardTitle>Nominees ({nominees.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {nominees.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Relation</TableHead>
                    <TableHead>Birthdate</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {nominees.map((nominee) => (
                    <TableRow key={nominee.id}>
                      <TableCell className="font-medium">{nominee.name}</TableCell>
                      <TableCell>{nominee.relation}</TableCell>
                      <TableCell>{nominee.birthdate ? formatDisplayDate(nominee.birthdate) : 'N/A'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-center text-gray-500 py-4">No nominees found</p>
            )}
          </CardContent>
        </Card>

        {/* Investments */}
        <Card>
          <CardHeader>
            <CardTitle>Investments ({investments.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {investments.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Scheme</TableHead>
                    <TableHead>Applicant Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Interest Rate</TableHead>
                    <TableHead>Investment Date</TableHead>
                    <TableHead>Maturity Date</TableHead>
                    <TableHead>Maturity Amount</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {investments.map((investment) => (
                    <TableRow key={investment.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{investment.scheme_name}</p>
                          <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={investment.applicant_type === 'primary'
                            ? 'bg-blue-50 text-blue-700 border-blue-200'
                            : 'bg-green-50 text-green-700 border-green-200'
                          }
                        >
                          {investment.applicant_type === 'primary' ? 'Primary' : 'Secondary'}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">{formatAmountInIndianFormat(investment.amount)}</TableCell>
                      <TableCell>{investment.interest_rate}%</TableCell>
                      <TableCell>{formatDisplayDate(investment.investment_date)}</TableCell>
                      <TableCell>{formatDisplayDate(investment.maturity_date)}</TableCell>
                      <TableCell className="font-medium">{formatAmountInIndianFormat(investment.maturity_amount)}</TableCell>
                      <TableCell>{getStatusBadge(investment.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-center text-gray-500 py-4">No investments found</p>
            )}
          </CardContent>
        </Card>

        {/* Transactions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions ({transactions.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {transactions.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Scheme</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Payment Mode</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{formatDisplayDate(transaction.transaction_date)}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{transaction.investments?.scheme_name || 'N/A'}</p>
                          <p className="text-sm text-gray-500">{transaction.investments?.scheme_code || 'N/A'}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={`capitalize ${getAmountTypeColor(transaction.amount_type)}`}>
                          {transaction.amount_type.replace(/_/g, ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">{formatAmountInIndianFormat(transaction.amount)}</TableCell>
                      <TableCell>{transaction.reference_number || 'N/A'}</TableCell>
                      <TableCell>{transaction.payment_mode || 'N/A'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-center text-gray-500 py-4">No transactions found</p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderMaturityReport = () => {
    const isMaturitySoon = (maturityDate: string) => {
      const maturity = new Date(maturityDate);
      const today = new Date();
      const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntilMaturity <= 30 && daysUntilMaturity > 0;
    };

    const getMaturityStatus = (investment: Investment) => {
      const maturity = new Date(investment.maturity_date);
      const today = new Date();
      const daysUntilMaturity = Math.ceil((maturity.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      const daysSinceMaturity = Math.abs(daysUntilMaturity);

      if (investment.status === 'withdrawn') {
        return { status: 'Withdrawn', days: 'Withdrawn', color: 'text-gray-600' };
      } else if (investment.status === 'reinvested') {
        return { status: 'Reinvested', days: 'Reinvested', color: 'text-purple-600' };
      } else if (investment.status === 'matured' && daysUntilMaturity <= 0) {
        return { status: 'Overdue', days: `${daysSinceMaturity} days overdue`, color: 'text-red-600' };
      } else if (investment.status === 'active' && daysUntilMaturity <= 0) {
        return { status: 'Overdue', days: `${daysSinceMaturity} days overdue`, color: 'text-red-600' };
      } else if (daysUntilMaturity <= 30) {
        return { status: 'Maturing Soon', days: `${daysUntilMaturity} days left`, color: 'text-orange-600' };
      } else {
        return { status: 'Active', days: `${daysUntilMaturity} days left`, color: 'text-green-600' };
      }
    };

    const activeInvestments = investments.filter(inv => inv.status === 'active').length;
    const maturedInvestments = investments.filter(inv => inv.status === 'matured').length;
    const maturingSoon = investments.filter(inv => isMaturitySoon(inv.maturity_date) && inv.status === 'active').length;
    const overdueInvestments = investments.filter(inv => {
      const maturity = new Date(inv.maturity_date);
      const today = new Date();
      return (inv.status === 'active' || inv.status === 'matured') && maturity < today;
    }).length;

    return (
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{activeInvestments}</p>
                  <p className="text-sm text-gray-600">Active Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{maturedInvestments}</p>
                  <p className="text-sm text-gray-600">Matured Investments</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{maturingSoon}</p>
                  <p className="text-sm text-gray-600">Maturing Soon (30 days)</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <IndianRupee className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-2xl font-bold">{overdueInvestments}</p>
                  <p className="text-sm text-gray-600">Overdue Maturity</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold w-16">S.No</TableHead>
                <TableHead className="font-semibold">Client Details</TableHead>
                <TableHead className="font-semibold">Village</TableHead>
                <TableHead className="font-semibold">Scheme</TableHead>
                <TableHead className="font-semibold text-right">Investment Amount</TableHead>
                <TableHead className="font-semibold text-right">Maturity Amount</TableHead>
                <TableHead className="font-semibold">Maturity Date</TableHead>
                <TableHead className="font-semibold text-center">Maturity Status</TableHead>
                <TableHead className="font-semibold">Current Status</TableHead>
                <TableHead className="font-semibold">Remarks</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment, index: number) => {
                const maturityInfo = getMaturityStatus(investment);
                const isOverdue = (investment.status === 'active' || investment.status === 'matured') && new Date(investment.maturity_date) < new Date();
                const isSoon = isMaturitySoon(investment.maturity_date) && investment.status === 'active';

                let rowClass = '';
                if (isOverdue) {
                  rowClass = 'bg-red-50 border-l-4 border-red-400';
                } else if (isSoon) {
                  rowClass = 'bg-orange-50 border-l-4 border-orange-400';
                }

                return (
                  <TableRow key={investment.id} className={`hover:bg-gray-50 ${rowClass}`}>
                    <TableCell className="font-medium text-center">
                      <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                    </TableCell>
                    <TableCell className="font-medium">
                      <div>
                        <div className="mb-2">
                          <p className="font-semibold">
                            <span className="text-blue-600">Primary:</span> {investment.clients ? `${investment.clients.first_name} ${investment.clients.last_name}` : 'N/A'}
                          </p>
                          <p className="text-xs text-gray-500">
                            Phone: {investment.clients?.mobile_number || 'N/A'} | CIF: {investment.primary_applicant_cif_id || investment.clients?.cif_id || 'N/A'}
                          </p>
                        </div>

                        {investment.secondary_applicant && (
                          <div>
                            <p className="font-semibold">
                              <span className="text-purple-600">Secondary:</span> {`${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}`}
                            </p>
                            <p className="text-xs text-gray-500">
                              Phone: {investment.secondary_applicant.mobile_number || 'N/A'} | CIF: {investment.secondary_applicant_cif_id || investment.secondary_applicant.cif_id || 'N/A'}
                            </p>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.clients?.village || 'N/A'}</p>
                        {investment.secondary_applicant && (
                          <p className="text-sm text-gray-500 mt-2">
                            <span className="text-purple-600">Secondary:</span> {investment.secondary_applicant.village || 'N/A'}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{investment.scheme_name}</p>
                        <p className="text-sm text-gray-500">{investment.interest_rate}% interest</p>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-blue-600">{formatAmountInIndianFormat(investment.amount)}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="font-bold text-green-600">{formatAmountInIndianFormat(investment.maturity_amount)}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{formatDisplayDate(investment.maturity_date)}</span>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge
                        variant={
                          investment.status === 'matured' ? "default" :
                            investment.status === 'withdrawn' ? "secondary" :
                              investment.status === 'reinvested' ? "outline" :
                                isOverdue ? "destructive" :
                                  isSoon ? "default" : "outline"
                        }
                        className={
                          investment.status === 'matured' ? "bg-blue-100 text-blue-800" :
                            investment.status === 'withdrawn' ? "bg-gray-100 text-gray-800" :
                              investment.status === 'reinvested' ? "bg-purple-100 text-purple-800" :
                                isOverdue ? "bg-red-100 text-red-800" :
                                  isSoon ? "bg-orange-100 text-orange-800" :
                                    "bg-green-100 text-green-800"
                        }
                      >
                        {maturityInfo.days}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          investment.status === 'active' ? "default" :
                            investment.status === 'matured' ? "outline" :
                              investment.status === 'withdrawn' ? "secondary" :
                                "outline"
                        }
                        className={
                          investment.status === 'active' ? "bg-green-100 text-green-800" :
                            investment.status === 'matured' ? "bg-blue-100 text-blue-800" :
                              investment.status === 'withdrawn' ? "bg-gray-100 text-gray-800" :
                                investment.status === 'reinvested' ? "bg-purple-100 text-purple-800" :
                                  ""
                        }
                      >
                        {maturityInfo.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-[200px]">
                      {editingRemarks === investment.id ? (
                        <div className="flex items-center gap-2">
                          <Input
                            value={remarksValue}
                            onChange={(e) => setRemarksValue(e.target.value)}
                            placeholder="Enter remarks..."
                            className="h-8 text-sm"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSaveRemarks(investment.id);
                              } else if (e.key === 'Escape') {
                                handleCancelEditRemarks();
                              }
                            }}
                            autoFocus
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSaveRemarks(investment.id)}
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                            title="Save"
                          >
                            ✓
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleCancelEditRemarks}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            title="Cancel"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 group">
                          <span className="text-sm text-gray-700 truncate flex-1" title={investment.remark || 'No remarks'}>
                            {investment.remark || 'No remarks'}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditRemarks(investment)}
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                            title="Edit Remarks"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  };


  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className=" space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
              <p className="text-gray-600 mt-1">Comprehensive investment and client reporting dashboard</p>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleExportToPDF} variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export PDF
              </Button>
              <Button onClick={exportToExcel} variant="outline" className="flex items-center gap-2">
                <FileSpreadsheet className="h-4 w-4" />
                Export Excel
              </Button>
            </div>
          </div>
        </div>


        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Report Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <div className="w-full">
                <Label>Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Status filter - only for Client and Investment reports */}
              {(activeTab === 'client' || activeTab === 'investment') && (
                <div className="w-full">
                  <Label>Status</Label>
                  <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {activeTab === 'client' ? (
                        <>
                          <SelectItem value="all">All Clients</SelectItem>
                          <SelectItem value="invested">Invested Clients</SelectItem>
                          <SelectItem value="no-invested">No Investment</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="matured">Matured</SelectItem>
                          <SelectItem value="reinvested">Reinvested</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              )}
              {/* Client filter - for all reports except client report */}
              {(activeTab === 'investment' || activeTab === 'transaction' || activeTab === 'commission' || activeTab === 'maturity' || activeTab === 'client_wise') && (
                <div className="w-full">
                  <Label>Client</Label>
                  <Select
                    value={filters.client}
                    onValueChange={(value) => {
                      setFilters({ ...filters, client: value });
                      if (activeTab === 'client_wise') {
                        fetchClientWiseReport();
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Clients</SelectItem>
                      {allClients.map((client) => (
                        <SelectItem key={client.id} value={client.id}>
                          {client.first_name} {client.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
              {activeTab === 'transaction' && (
                <div className="w-full">
                  <Label>Transaction Type</Label>
                  <Select value={filters.transactionType} onValueChange={(value) => setFilters({ ...filters, transactionType: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="investment">Investment</SelectItem>
                      <SelectItem value="interest_payout">Interest Payout</SelectItem>
                      <SelectItem value="maturity_payout">Maturity Payout</SelectItem>
                      <SelectItem value="penalty">Penalty</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              <div className="w-full lg:col-span-2">
                <Label>Date Filter</Label>
                <EnhancedDateFilter
                  value={dateFilter}
                  onChange={setDateFilter}
                  customRange={customDateRange}
                  onCustomRangeChange={setCustomDateRange}
                />
              </div>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              <Button onClick={fetchReportData} disabled={loading}>
                {loading ? 'Loading...' : 'Apply Filters'}
              </Button>
              <Button onClick={resetFilters} variant="outline">
                Reset Filters
              </Button>
            </div>
          </CardContent>
        </Card>


        {/* Report Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="w-full overflow-x-auto">
            <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground min-w-max">
              <TabsTrigger value="maturity" className="whitespace-nowrap px-3 py-1.5 text-sm">Maturity Report</TabsTrigger>
              <TabsTrigger value="investment" className="whitespace-nowrap px-3 py-1.5 text-sm">Investment Report</TabsTrigger>
              <TabsTrigger value="client" className="whitespace-nowrap px-3 py-1.5 text-sm">Client Report</TabsTrigger>
              <TabsTrigger value="client_wise" className="whitespace-nowrap px-3 py-1.5 text-sm">Client-Wise Report</TabsTrigger>
              <TabsTrigger value="transaction" className="whitespace-nowrap px-3 py-1.5 text-sm">Transaction Report</TabsTrigger>
              <TabsTrigger value="commission" className="whitespace-nowrap px-3 py-1.5 text-sm">Commission Report</TabsTrigger>
            </TabsList>
          </div>


          <TabsContent value="client" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Client Report</h3>
                <p className="text-sm text-gray-600">{clients.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderClientReport()
                )}
              </div>
            </div>
          </TabsContent>


          <TabsContent value="investment" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Investment Report</h3>
                <p className="text-sm text-gray-600">{investments.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderInvestmentReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="transaction" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Transaction Report</h3>
                <p className="text-sm text-gray-600">{transactions.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderTransactionReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="commission" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Commission Report</h3>
                <p className="text-sm text-gray-600">{investments.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderCommissionReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="client_wise" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Client-Wise Report</h3>
                <p className="text-sm text-gray-600">
                  {selectedClientData.client ?
                    `Showing details for ${selectedClientData.client.first_name} ${selectedClientData.client.last_name}` :
                    "Select a client to view detailed report"}
                </p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading client data...
                    </div>
                  </div>
                ) : (
                  renderClientWiseReport()
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="maturity" className="space-y-0">
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="border-b px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">Maturity Report</h3>
                <p className="text-sm text-gray-600">{investments.length} records found</p>
              </div>
              <div className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading reports...
                    </div>
                  </div>
                ) : (
                  renderMaturityReport()
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};


export default Reports;



