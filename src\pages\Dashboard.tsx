import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, TrendingUp, IndianRupee, Calendar, PieChart, BarChart3, Activity, Target, Eye } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON> as RePie<PERSON>hart, Cell, LineChart, Line, AreaChart, Area, Pie, Legend } from 'recharts';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { formatDisplayDate } from '@/utils/dateFormat';
import { EnhancedDateFilter, DateRange, getDateRangeValues } from '@/components/ui/enhanced-date-filter';
import { format, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval, startOfWeek, endOfWeek } from 'date-fns';

// Define types for dashboard data
interface Client {
  id: string;
  first_name: string;
  last_name: string;
  created_at?: string;
  email?: string;
  mobile_number?: string;
}
interface Investment {
  id: string;
  amount: number;
  investment_date: string;
  maturity_date: string;
  maturity_amount: number;
  scheme_name: string;
  scheme_code?: string;
  commission_percentage?: number;
  tds_amount?: number;
  clients?: Client;
}
interface Scheme {
  id: string;
  name: string;
}
interface Transaction {
  id: string;
  amount: number;
  amount_type: string;
  transaction_date: string;
  reference_number: string;
  investments?: {
    id: string;
    scheme_name: string;
    clients?: Client;
  };
}
interface MaturityClient {
  id: string;
  maturity_date: string;
  maturity_amount: number;
  scheme_name: string;
  clients: Client;
  status: string;
}
interface DashboardStats {
  clients: Client[];
  investments: Investment[];
  schemes: Scheme[];
  transactions: Transaction[];
  maturityClients: MaturityClient[];
}



const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<DashboardStats>({
    clients: [],
    investments: [],
    schemes: [],
    transactions: [],
    maturityClients: []
  });
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange>();

  useEffect(() => {
    fetchDashboardData();
  }, [dateFilter, customDateRange]);

  // Filter data by selected date range
  const filterByDateRange = <T extends { investment_date?: string; maturity_date?: string; created_at?: string; transaction_date?: string }>(arr: T[], key: keyof T) => {
    const dateRange = getDateRangeValues(dateFilter, customDateRange, true);
    if (!dateRange || dateFilter === 'all') return arr;
    
    return arr.filter(item => {
      const dateStr = item[key];
      if (!dateStr) return false;
      const itemDate = new Date(dateStr);
      const fromDate = new Date(dateRange.from);
      const toDate = new Date(dateRange.to);
      return itemDate >= fromDate && itemDate <= toDate;
    });
  };

  const fetchDashboardData = async () => {
    try {
      const [clientsRes, investmentsRes, schemesRes, transactionsRes] = await Promise.all([
        supabase.from('clients').select('id, first_name, last_name, created_at, email, mobile_number').eq('is_deleted', false),
        supabase.from('investments').select('*'),
        supabase.from('schemes').select('id, name').eq('is_active', true),
        supabase.from('transactions').select(`
          id, amount, amount_type, transaction_date, reference_number,
          investments(id, scheme_name, clients:clients!investments_client_id_fkey(id, first_name, last_name))
        `).order('created_at', { ascending: false }).limit(5)
      ]);

      const clients = clientsRes.data || [];
      const investments = investmentsRes.data || [];
      const schemes = schemesRes.data || [];
      const transactions = transactionsRes.data || [];

      // This month maturity clients (always current month, not filtered by year)
      const now = new Date();
      const currentMonth = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
      const maturityClientsData = await supabase
        .from('investments')
        .select(`
          id, maturity_date, maturity_amount, scheme_name, status,
          clients:clients!investments_client_id_fkey(id, first_name, last_name, email, mobile_number)
        `)
        .gte('maturity_date', currentMonth + '-01')
        .lte('maturity_date', currentMonth + '-31')
        .in('status', ['active', 'matured', 'reinvested'])
        .limit(5);

      setData({
        clients,
        investments,
        schemes,
        transactions,
        maturityClients: maturityClientsData.data || []
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700';
      case 'matured':
        return 'bg-blue-100 text-blue-700';
      case 'reinvested':
        return 'bg-orange-100 text-orange-700';
      case 'transferred':
        return 'bg-purple-100 text-purple-700';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-600';
      default:
        return 'bg-yellow-100 text-yellow-700';
    }
  };

  // Dynamic chart data based on date filter
  const getInvestmentsChartData = () => {
    // Handle "all time" case - show monthly data for current year
    if (dateFilter === 'all') {
      const currentYear = new Date().getFullYear();
      const monthlyData = [];
      for (let i = 0; i < 12; i++) {
        const monthStart = new Date(currentYear, i, 1);
        const monthEnd = new Date(currentYear, i + 1, 0);
        const monthInvestments = data.investments.filter(inv => {
          if (!inv.investment_date) return false;
          const invDate = new Date(inv.investment_date);
          return invDate >= monthStart && invDate <= monthEnd;
        });
        const totalAmount = monthInvestments.reduce((sum, inv) => sum + (Number(inv.amount) || 0), 0);
        monthlyData.push({
          period: format(monthStart, 'MMM'),
          investments: monthInvestments.length,
          amount: Number((totalAmount / 100000).toFixed(2))
        });
      }
      return monthlyData;
    }

    const dateRange = getDateRangeValues(dateFilter, customDateRange, false);
    if (!dateRange) return [];
    
    const filteredInvestments = filterByDateRange(data.investments, 'investment_date');
    const fromDate = new Date(dateRange.from + 'T00:00:00');
    const toDate = new Date(dateRange.to + 'T23:59:59');
    const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let intervals: Date[] = [];
    let formatKey = '';
    
    if (daysDiff <= 7) {
      intervals = eachDayOfInterval({ start: fromDate, end: toDate });
      formatKey = 'dd MMM';
    } else if (daysDiff <= 90) {
      intervals = eachWeekOfInterval({ start: fromDate, end: toDate }, { weekStartsOn: 1 });
      formatKey = 'dd MMM';
    } else {
      intervals = eachMonthOfInterval({ start: fromDate, end: toDate });
      formatKey = 'MMM yyyy';
    }
    
    return intervals.map(intervalStart => {
      let intervalEnd: Date;
      
      if (daysDiff <= 7) {
        intervalEnd = new Date(intervalStart);
        intervalEnd.setHours(23, 59, 59, 999);
      } else if (daysDiff <= 90) {
        intervalEnd = endOfWeek(intervalStart, { weekStartsOn: 1 });
        if (intervalEnd > toDate) intervalEnd = toDate;
      } else {
        intervalEnd = new Date(intervalStart.getFullYear(), intervalStart.getMonth() + 1, 0);
        if (intervalEnd > toDate) intervalEnd = toDate;
      }
      
      const periodInvestments = filteredInvestments.filter(inv => {
        if (!inv.investment_date) return false;
        const invDate = new Date(inv.investment_date);
        return invDate >= intervalStart && invDate <= intervalEnd;
      });
      
      const totalAmount = periodInvestments.reduce((sum, inv) => sum + (Number(inv.amount) || 0), 0);
      
      return {
        period: format(intervalStart, formatKey),
        investments: periodInvestments.length,
        amount: Number((totalAmount / 100000).toFixed(2))
      };
    });
  };


  // Scheme Distribution chart
  const getSchemeDistributionData = () => {
    const filteredInvestments = filterByDateRange(data.investments, 'investment_date');
    const schemeMap: Record<string, number> = {};
    filteredInvestments.forEach(inv => {
      if (inv.scheme_name) {
        schemeMap[inv.scheme_name] = (schemeMap[inv.scheme_name] || 0) + Number(inv.amount);
      }
    });
    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];
    return Object.entries(schemeMap).map(([name, value], index) => ({
      name,
      value: Number(value),
      color: colors[index % colors.length]
    }));
  };

  // Client Growth chart
  const getClientGrowthData = () => {
    // Handle "all time" case
    if (dateFilter === 'all') {
      const currentYear = new Date().getFullYear();
      const monthlyData = [];
      for (let i = 0; i < 12; i++) {
        const monthStart = new Date(currentYear, i, 1);
        const monthEnd = new Date(currentYear, i + 1, 0);
        const monthClients = data.clients.filter(client => {
          if (!client.created_at) return false;
          const createdDate = new Date(client.created_at);
          return createdDate >= monthStart && createdDate <= monthEnd;
        });
        monthlyData.push({
          period: format(monthStart, 'MMM'),
          clients: monthClients.length
        });
      }
      return monthlyData;
    }

    const dateRange = getDateRangeValues(dateFilter, customDateRange, false);
    if (!dateRange) return [];
    
    const filteredClients = filterByDateRange(data.clients, 'created_at');
    const fromDate = new Date(dateRange.from + 'T00:00:00');
    const toDate = new Date(dateRange.to + 'T23:59:59');
    const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let intervals: Date[] = [];
    let formatKey = '';
    
    if (daysDiff <= 7) {
      intervals = eachDayOfInterval({ start: fromDate, end: toDate });
      formatKey = 'dd MMM';
    } else if (daysDiff <= 90) {
      intervals = eachWeekOfInterval({ start: fromDate, end: toDate }, { weekStartsOn: 1 });
      formatKey = 'dd MMM';
    } else {
      intervals = eachMonthOfInterval({ start: fromDate, end: toDate });
      formatKey = 'MMM yyyy';
    }
    
    return intervals.map(intervalStart => {
      let intervalEnd: Date;
      
      if (daysDiff <= 7) {
        intervalEnd = new Date(intervalStart);
        intervalEnd.setHours(23, 59, 59, 999);
      } else if (daysDiff <= 90) {
        intervalEnd = endOfWeek(intervalStart, { weekStartsOn: 1 });
        if (intervalEnd > toDate) intervalEnd = toDate;
      } else {
        intervalEnd = new Date(intervalStart.getFullYear(), intervalStart.getMonth() + 1, 0);
        if (intervalEnd > toDate) intervalEnd = toDate;
      }
      
      const periodClients = filteredClients.filter(client => {
        if (!client.created_at) return false;
        const createdDate = new Date(client.created_at);
        return createdDate >= intervalStart && createdDate <= intervalEnd;
      });
      
      return {
        period: format(intervalStart, formatKey),
        clients: periodClients.length
      };
    });
  };


  // Maturity Overview chart
  const getMaturityOverviewData = () => {
    // Handle "all time" case
    if (dateFilter === 'all') {
      const currentYear = new Date().getFullYear();
      const monthlyData = [];
      for (let i = 0; i < 12; i++) {
        const monthStart = new Date(currentYear, i, 1);
        const monthEnd = new Date(currentYear, i + 1, 0);
        const monthMaturities = data.investments.filter(inv => {
          if (!inv.maturity_date) return false;
          const maturityDate = new Date(inv.maturity_date);
          return maturityDate >= monthStart && maturityDate <= monthEnd;
        });
        const totalAmount = monthMaturities.reduce((sum, inv) => sum + (Number(inv.maturity_amount) || 0), 0);
        monthlyData.push({
          period: format(monthStart, 'MMM'),
          count: monthMaturities.length,
          amount: Number((totalAmount / 100000).toFixed(2))
        });
      }
      return monthlyData;
    }

    const dateRange = getDateRangeValues(dateFilter, customDateRange, false);
    if (!dateRange) return [];
    
    const filteredMaturities = filterByDateRange(data.investments, 'maturity_date');
    const fromDate = new Date(dateRange.from + 'T00:00:00');
    const toDate = new Date(dateRange.to + 'T23:59:59');
    const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let intervals: Date[] = [];
    let formatKey = '';
    
    if (daysDiff <= 7) {
      intervals = eachDayOfInterval({ start: fromDate, end: toDate });
      formatKey = 'dd MMM';
    } else if (daysDiff <= 90) {
      intervals = eachWeekOfInterval({ start: fromDate, end: toDate }, { weekStartsOn: 1 });
      formatKey = 'dd MMM';
    } else {
      intervals = eachMonthOfInterval({ start: fromDate, end: toDate });
      formatKey = 'MMM yyyy';
    }
    
    return intervals.map(intervalStart => {
      let intervalEnd: Date;
      
      if (daysDiff <= 7) {
        intervalEnd = new Date(intervalStart);
        intervalEnd.setHours(23, 59, 59, 999);
      } else if (daysDiff <= 90) {
        intervalEnd = endOfWeek(intervalStart, { weekStartsOn: 1 });
        if (intervalEnd > toDate) intervalEnd = toDate;
      } else {
        intervalEnd = new Date(intervalStart.getFullYear(), intervalStart.getMonth() + 1, 0);
        if (intervalEnd > toDate) intervalEnd = toDate;
      }
      
      const periodMaturities = filteredMaturities.filter(inv => {
        if (!inv.maturity_date) return false;
        const maturityDate = new Date(inv.maturity_date);
        return maturityDate >= intervalStart && maturityDate <= intervalEnd;
      });
      
      const totalAmount = periodMaturities.reduce((sum, inv) => sum + (Number(inv.maturity_amount) || 0), 0);
      
      return {
        period: format(intervalStart, formatKey),
        count: periodMaturities.length,
        amount: Number((totalAmount / 100000).toFixed(2))
      };
    });
  };

  const formatAmount = (amount: number) => {
    if (amount >= 1e7) return `₹${(amount / 1e7).toFixed(2)} Cr`;
    if (amount >= 1e5) return `₹${(amount / 1e5).toFixed(2)} L`;
    if (amount >= 1e3) return `₹${(amount / 1e3).toFixed(2)} K`;
    return `₹${amount.toFixed(2)}`;
  };

  // Commission Overview chart
  const getCommissionData = () => {
    // Handle "all time" case
    if (dateFilter === 'all') {
      const currentYear = new Date().getFullYear();
      const monthlyData = [];
      for (let i = 0; i < 12; i++) {
        const monthStart = new Date(currentYear, i, 1);
        const monthEnd = new Date(currentYear, i + 1, 0);
        const monthInvestments = data.investments.filter(inv => {
          if (!inv.investment_date) return false;
          const invDate = new Date(inv.investment_date);
          return invDate >= monthStart && invDate <= monthEnd;
        });
        const commission = monthInvestments.reduce((sum, inv) => {
          const amount = Number(inv.amount) || 0;
          const commissionPercent = Number(inv.commission_percentage) || 0;
          return sum + (amount * commissionPercent) / 100;
        }, 0);
        const tds = monthInvestments.reduce((sum, inv) => sum + (Number(inv.tds_amount) || 0), 0);
        monthlyData.push({
          period: format(monthStart, 'MMM'),
          commission: Number((commission / 1000).toFixed(2)),
          tds: Number((tds / 1000).toFixed(2)),
          net: Number(((commission - tds) / 1000).toFixed(2))
        });
      }
      return monthlyData;
    }

    const dateRange = getDateRangeValues(dateFilter, customDateRange, false);
    if (!dateRange) return [];
    
    const filteredInvestments = filterByDateRange(data.investments, 'investment_date');
    const fromDate = new Date(dateRange.from + 'T00:00:00');
    const toDate = new Date(dateRange.to + 'T23:59:59');
    const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    let intervals: Date[] = [];
    let formatKey = '';
    
    if (daysDiff <= 7) {
      intervals = eachDayOfInterval({ start: fromDate, end: toDate });
      formatKey = 'dd MMM';
    } else if (daysDiff <= 90) {
      intervals = eachWeekOfInterval({ start: fromDate, end: toDate }, { weekStartsOn: 1 });
      formatKey = 'dd MMM';
    } else {
      intervals = eachMonthOfInterval({ start: fromDate, end: toDate });
      formatKey = 'MMM yyyy';
    }
    
    return intervals.map(intervalStart => {
      let intervalEnd: Date;
      
      if (daysDiff <= 7) {
        intervalEnd = new Date(intervalStart);
        intervalEnd.setHours(23, 59, 59, 999);
      } else if (daysDiff <= 90) {
        intervalEnd = endOfWeek(intervalStart, { weekStartsOn: 1 });
        if (intervalEnd > toDate) intervalEnd = toDate;
      } else {
        intervalEnd = new Date(intervalStart.getFullYear(), intervalStart.getMonth() + 1, 0);
        if (intervalEnd > toDate) intervalEnd = toDate;
      }
      
      const periodInvestments = filteredInvestments.filter(inv => {
        if (!inv.investment_date) return false;
        const invDate = new Date(inv.investment_date);
        return invDate >= intervalStart && invDate <= intervalEnd;
      });
      
      const commission = periodInvestments.reduce((sum, inv) => {
        const amount = Number(inv.amount) || 0;
        const commissionPercent = Number(inv.commission_percentage) || 0;
        return sum + (amount * commissionPercent) / 100;
      }, 0);
      
      const tds = periodInvestments.reduce((sum, inv) => sum + (Number(inv.tds_amount) || 0), 0);
      
      return {
        period: format(intervalStart, formatKey),
        commission: Number((commission / 1000).toFixed(2)),
        tds: Number((tds / 1000).toFixed(2)),
        net: Number(((commission - tds) / 1000).toFixed(2))
      };
    });
  };


  // Calculate stats for cards
  const getDashboardStats = () => {
    const filteredInvestments = filterByDateRange(data.investments, 'investment_date');
    const filteredClients = filterByDateRange(data.clients, 'created_at');
    const totalClients = filteredClients.length;
    const totalInvestments = filteredInvestments.length;
    const totalAmount = filteredInvestments.reduce((sum, inv) => sum + (Number(inv.amount) || 0), 0);
    const activeSchemes = data.schemes.length;
    return {
      totalClients,
      totalInvestments,
      totalAmount,
      activeSchemes
    };
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading dashboard...</div>;
  }

  const stats = getDashboardStats();

  return (
    <div className="space-y-6">
      {/* Global Date Filter */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold">Dashboard</h1>
        <div className="flex flex-col sm:flex-row gap-2 sm:items-center">
          <EnhancedDateFilter
            value={dateFilter}
            onChange={setDateFilter}
            customRange={customDateRange}
            onCustomRangeChange={setCustomDateRange}
          />
          <Button
            onClick={fetchDashboardData}
            variant="outline"
            className="w-full sm:w-auto"
            size="sm"
          >
            <Activity className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">Refresh Data</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Total Clients</CardTitle>
            <Users className="h-3 w-3 md:h-4 md:w-4 text-muted-foreground flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">{stats.totalClients}</div>
            <p className="text-xs text-muted-foreground truncate">Active client accounts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Total Investments</CardTitle>
            <TrendingUp className="h-3 w-3 md:h-4 md:w-4 text-muted-foreground flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">{stats.totalInvestments}</div>
            <p className="text-xs text-muted-foreground truncate">Investment portfolios</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Total Amount</CardTitle>
            <IndianRupee className="h-3 w-3 md:h-4 md:w-4 text-muted-foreground flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">₹{(stats.totalAmount / 1000).toFixed(1)}K</div>
            <p className="text-xs text-muted-foreground truncate">Assets under management</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 md:pb-2">
            <CardTitle className="text-xs md:text-sm font-medium truncate">Active Schemes</CardTitle>
            <Target className="h-3 w-3 md:h-4 md:w-4 text-muted-foreground flex-shrink-0" />
          </CardHeader>
          <CardContent className="pt-1 md:pt-0">
            <div className="text-lg md:text-2xl font-bold">{stats.activeSchemes}</div>
            <p className="text-xs text-muted-foreground truncate">Investment products</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1  lg:grid-cols-2 gap-6">
        {/* Monthly Investments Chart */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Investments Overview
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={getInvestmentsChartData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'amount' ? `₹${value}L` : value,
                    name === 'amount' ? 'Amount' : 'Investments'
                  ]}
                />
                <Legend />
                <Bar
                  dataKey="investments"
                  name="Number of Investments"
                  fill="#8884d8"
                />
                <Bar
                  dataKey="amount"
                  name="Amount (in Lakhs)"
                  fill="#82ca9d"
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Scheme Distribution Chart */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="text-sm sm:text-base">Scheme Distribution</span>
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-3 sm:p-6">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
              {/* Pie Chart */}
              <div className="flex justify-center items-center">
                <ResponsiveContainer width="100%" height={250} className="sm:!h-[280px]">
                  <RePieChart>
                    <Pie
                      data={getSchemeDistributionData()}
                      cx="50%"
                      cy="50%"
                      outerRadius={window.innerWidth < 640 ? 70 : 90}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ percent }) => `${(percent * 100).toFixed(1)}%`}
                      labelLine={false}
                      fontSize={window.innerWidth < 640 ? 10 : 12}
                    >
                      {getSchemeDistributionData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [`₹${(Number(value) / 1000).toFixed(0)}K`, 'Amount']}
                      labelFormatter={(label) => `Scheme: ${label}`}
                      contentStyle={{
                        fontSize: window.innerWidth < 640 ? '12px' : '14px',
                        padding: window.innerWidth < 640 ? '8px' : '12px'
                      }}
                    />
                  </RePieChart>
                </ResponsiveContainer>
              </div>

              {/* Scheme List with Percentages */}
              <div className="space-y-2 sm:space-y-3">
                <h4 className="font-semibold text-xs sm:text-sm text-gray-700 mb-2 sm:mb-3">Scheme Breakdown</h4>
                <div className="max-h-[200px] sm:max-h-[250px] overflow-y-auto space-y-1.5 sm:space-y-2 scrollbar-hide">
                  {getSchemeDistributionData().map((entry, index) => {
                    const totalValue = getSchemeDistributionData().reduce((sum, item) => sum + item.value, 0);
                    const percentage = ((entry.value / totalValue) * 100).toFixed(1);
                    return (
                      <div key={index} className="flex items-center justify-between p-1.5 sm:p-2 bg-gray-50 rounded-md">
                        <div className="flex items-center gap-1.5 sm:gap-2 min-w-0 flex-1">
                          <div
                            className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full flex-shrink-0"
                            style={{ backgroundColor: entry.color }}
                          ></div>
                          <span className="text-xs sm:text-sm font-medium text-gray-700 truncate">
                            {entry.name}
                          </span>
                        </div>
                        <div className="text-right flex-shrink-0 ml-2">
                          <div className="text-xs sm:text-sm font-semibold text-gray-900">
                            {percentage}%
                          </div>
                          <div className="text-xs text-gray-500">
                            ₹{(entry.value / 1000).toFixed(0)}K
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  {getSchemeDistributionData().length === 0 && (
                    <p className="text-gray-500 text-center py-4 text-xs sm:text-sm">No scheme data available</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Client Growth Chart */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Client Growth
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={getClientGrowthData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="clients"
                  name="New Clients"
                  stroke="#8884d8"
                  fill="#8884d8"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Upcoming Maturities Chart */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Upcoming Maturities
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={getMaturityOverviewData()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'amount' ? `₹${value}L` : value,
                    name === 'amount' ? 'Amount' : 'Maturities'
                  ]}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="count"
                  name="Number of Maturities"
                  stroke="#ff7300"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="amount"
                  name="Maturity Amount (in Lakhs)"
                  stroke="#82ca9d"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* New Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* This Month Maturity Clients */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              This Month Maturity Clients
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/clients?filter=maturity')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.maturityClients.length > 0 ? (
                data.maturityClients.map((client: MaturityClient) => (
                  client.clients && client.clients.id ? (
                    <div key={client.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">
                          {client.clients.first_name} {client.clients.last_name}
                        </p>
                        <p className="text-sm text-gray-500">{client.scheme_name}</p>
                        <p className="text-sm text-green-600">
                          ₹{Number(client.maturity_amount).toLocaleString()}
                        </p>
                        <p className="text-sm text-gray-500">
                          Maturity: {formatDisplayDate(client.maturity_date)}
                        </p>
                        <span className={`inline-block text-xs mt-1 px-2 py-1 rounded-full ${getStatusColor(client.status)}`}>
                          {client.status}
                        </span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/clients/${client.clients.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Profile
                      </Button>
                    </div>
                  ) : null
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No maturities this month</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <IndianRupee className="h-5 w-5" />
              Recent Transactions
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/transactions')}
            >
              View All
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.transactions.length > 0 ? (
                data.transactions.map((transaction: Transaction) => (
                  transaction.investments && transaction.investments.clients && transaction.investments.clients.id ? (
                    <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{transaction.investments.clients.first_name} {transaction.investments.clients.last_name}</p>
                        <p className="text-sm text-gray-500">{transaction.amount_type}</p>
                        <p className="text-sm text-blue-600">₹{Number(transaction.amount).toLocaleString()}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">{formatDisplayDate(transaction.transaction_date)}</p>
                        <p className="text-xs text-gray-400">{transaction.reference_number}</p>
                      </div>
                    </div>
                  ) : null
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent transactions</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Commission Chart */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <IndianRupee className="h-5 w-5" />
              Commission Overview (in thousands)
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={getCommissionData()}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip formatter={(value) => [`₹${value}K`, '']} />
              <Legend />
              <Bar
                dataKey="commission"
                name="Gross Commission"
                fill="#8884d8"
              />
              <Bar
                dataKey="tds"
                name="TDS Deducted"
                fill="#ff7300"
              />
              <Bar
                dataKey="net"
                name="Net Commission"
                fill="#82ca9d"
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;