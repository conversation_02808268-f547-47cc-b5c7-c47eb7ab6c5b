-- Create client-documents bucket (only if it doesn't exist)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'client-documents',
  'client-documents',
  false,
  10485760, -- 10MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
)
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for client-documents bucket (only if they don't exist)
DROP POLICY IF EXISTS "Allow authenticated users to upload client documents" ON storage.objects;
CREATE POLICY "Allow authenticated users to upload client documents" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'client-documents' AND
  auth.role() = 'authenticated'
);

DROP POLICY IF EXISTS "Allow authenticated users to view client documents" ON storage.objects;
CREATE POLICY "Allow authenticated users to view client documents" ON storage.objects
FOR SELECT USING (
  bucket_id = 'client-documents' AND
  auth.role() = 'authenticated'
);

DROP POLICY IF EXISTS "Allow authenticated users to update client documents" ON storage.objects;
CREATE POLICY "Allow authenticated users to update client documents" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'client-documents' AND
  auth.role() = 'authenticated'
);

DROP POLICY IF EXISTS "Allow authenticated users to delete client documents" ON storage.objects;
CREATE POLICY "Allow authenticated users to delete client documents" ON storage.objects
FOR DELETE USING (
  bucket_id = 'client-documents' AND
  auth.role() = 'authenticated'
);