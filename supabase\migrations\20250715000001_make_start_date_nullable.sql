-- Make start_date nullable in investments table
-- This migration allows start_date to be null since we're removing it from the forms
-- and using only investment_date for calculations

ALTER TABLE public.investments 
ALTER COLUMN start_date DROP NOT NULL;

-- Update existing records to set start_date to investment_date where they differ
-- This ensures data consistency before we stop using start_date
UPDATE public.investments 
SET start_date = investment_date 
WHERE start_date != investment_date;

-- Add comment to document the change
COMMENT ON COLUMN public.investments.start_date IS 'Legacy field - now nullable. Use investment_date for all calculations.';
