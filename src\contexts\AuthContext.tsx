
import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  userRole: string | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | Error | null }>;
  signInWithPhone: (phone: string, password: string) => Promise<{ error: AuthError | Error | null }>;
  signUp: (email: string, password: string) => Promise<{ error: AuthError | Error | null }>;
  signUpWithPhone: (phone: string, password: string) => Promise<{ error: AuthError | Error | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: AuthError | Error | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Fetch user role
          setTimeout(async () => {
            try {
              const { data: roleData, error } = await supabase.rpc('get_user_role');
              if (!error && roleData) {
                setUserRole(roleData as string);
              }
            } catch (error) {
              console.error('Error fetching user role:', error);
            }
          }, 0);
        } else {
          setUserRole(null);
        }

        setLoading(false);
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }

    return { error };
  };

  const signInWithPhone = async (phone: string, password: string) => {
    try {
      // Use the database function to get email by phone number
      const { data: email, error: emailError } = await supabase.rpc('get_email_by_phone', {
        phone_number: phone
      });

      if (emailError || !email || typeof email !== 'string') {
        toast({
          title: "Error",
          description: "Phone number not found or account is disabled",
          variant: "destructive",
        });
        return { error: emailError || new Error("Phone number not found") };
      }

      // Now sign in using the found email and provided password
      const { error } = await supabase.auth.signInWithPassword({
        email: email,
        password,
      });

      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      }

      return { error };
    } catch (err) {
      const error = err as Error;
      toast({
        title: "Error",
        description: "An unexpected error occurred during phone login",
        variant: "destructive",
      });
      return { error };
    }
  };

  const signUp = async (email: string, password: string) => {
    const redirectUrl = `${window.location.origin}/`;

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl
      }
    });

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Success",
        description: "Please check your email for verification link",
      });
    }

    return { error };
  };

  const signUpWithPhone = async (phone: string, password: string) => {
    try {
      // Create a temporary email for Supabase auth (since Supabase requires email)
      const tempEmail = `${phone.replace(/[^0-9]/g, '')}@temp.local`;

      const { data, error } = await supabase.auth.signUp({
        email: tempEmail,
        password,
        phone,
      });

      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      // Update the users table with the phone number
      if (data.user) {
        const { error: updateError } = await supabase
          .from('users')
          .update({ mobile: phone })
          .eq('id', data.user.id);

        if (updateError) {
          console.error('Error updating mobile number:', updateError);
        }
      }

      toast({
        title: "Success",
        description: "Account created successfully",
      });

      return { error };
    } catch (err) {
      const error = err as Error;
      toast({
        title: "Error",
        description: "An unexpected error occurred during signup",
        variant: "destructive",
      });
      return { error };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setUser(null);
    setSession(null);
    setUserRole(null);
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Success",
        description: "Password reset email sent",
      });
    }

    return { error };
  };

  const value = {
    user,
    session,
    userRole,
    loading,
    signIn,
    signInWithPhone,
    signUp,
    signUpWithPhone,
    signOut,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
