import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  FileDown,
  Mail,
  MessageCircle,
  Edit,
  Phone,
  User,
  Calendar,
  IndianRupee,
  TrendingUp,
  Clock,
  Target,
  Percent,
  FileText
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import ClientDetailsCard from '@/components/investment/ClientDetailsCard';
import { exportToPDF } from '@/utils/pdfExport';
import { formatDisplayDate } from '@/utils/dateFormat';

interface InvestmentDetail {
  id: string;
  amount: number;
  investment_date: string;
  start_date: string;
  maturity_date: string;
  maturity_amount: number;
  status: string;
  investment_type: string;
  remark: string;
  actual_profit: number;
  tds_amount: number;
  scheme_name: string;
  scheme_code: string;
  interest_rate: number;
  interest_type: string;
  tenure_months: number;
  lock_in_period_months: number;
  payout_status: string;
  compounding_frequency: string;
  min_amount: number;
  max_amount: number;
  commission_percentage: number;
  supports_sip: boolean;
  primary_applicant_cif_id: string | null;
  secondary_applicant_cif_id: string | null;

  client: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    mobile_number: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
    village: string | null;
    cif_id: string | null;
  };
  secondary_applicant?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    mobile_number: string;
    village: string | null;
    cif_id: string | null;
  };
  nominee?: {
    name: string;
    relation: string;
  };
}

const InvestmentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [investment, setInvestment] = useState<InvestmentDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [sendingEmail, setSendingEmail] = useState(false);
  const [sendingWhatsApp, setSendingWhatsApp] = useState(false);
  const [sendingSMS, setSendingSMS] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState({
    email_enabled: false,
    sms_enabled: false,
    whatsapp_enabled: false
  });

  useEffect(() => {
    if (id) {
      fetchInvestmentDetail();
      fetchNotificationSettings();
    }
  }, [id]);

  const fetchNotificationSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_settings')
        .select('email_enabled, sms_enabled, whatsapp_enabled')
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching notification settings:', error);
        return;
      }

      if (data) {
        setNotificationSettings(data);
      }
    } catch (error) {
      console.error('Error fetching notification settings:', error);
    }
  };

  const fetchInvestmentDetail = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          client:clients!client_id (
            id, first_name, last_name, email, mobile_number,
            address, city, state, pincode, village, cif_id
          ),
          secondary_applicant:clients!second_applicant_id (
            id, first_name, last_name, email, mobile_number, village, cif_id
          ),
          nominee:nominees!nominee_id (
            name, relation
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      setInvestment(data);
    } catch (error) {
      console.error('Error fetching investment:', error);
      toast({
        title: "Error",
        description: "Failed to fetch investment details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Utility function to format amount in Indian format
  const formatAmountInIndianFormat = (amount: number): string => {
    if (amount >= 10000000) { // 1 Crore
      return `₹${(amount / 10000000).toFixed(1)}Cr`;
    } else if (amount >= 100000) { // 1 Lakh
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else if (amount >= 1000) { // 1 Thousand
      return `₹${(amount / 1000).toFixed(1)}K`;
    } else {
      return `₹${amount.toLocaleString()}`;
    }
  };

  const generatePDFReport = async () => {
    if (!investment) {
      toast({
        title: "Error",
        description: "No investment data available",
        variant: "destructive",
      });
      return;
    }

    try {
      const grossProfit = investment.maturity_amount - investment.amount;
      const profitPercentage = ((grossProfit / investment.amount) * 100).toFixed(2);
      const monthlyReturn = grossProfit / investment.tenure_months;
      const daysToMaturity = Math.ceil((new Date(investment.maturity_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

      const filterSummary = [
        { label: 'Investment ID', value: investment.id, applied: true },
        { label: 'Client Name', value: `${investment.client.first_name} ${investment.client.last_name}`, applied: true },
        { label: 'Scheme', value: `${investment.scheme_name} (${investment.scheme_code})`, applied: true },
        { label: 'Status', value: investment.status, applied: true },
        { label: 'Generated Date', value: new Date().toLocaleDateString(), applied: true }
      ];

      const tableContent = `
        <div class="investment-overview">
          <h3 style="color: #4f46e5; margin-bottom: 20px; font-size: 18px; font-weight: 600;">Investment Details Report</h3>
          <p style="margin-bottom: 25px; color: #666; font-size: 14px;">Comprehensive investment information for ${investment.client.first_name} ${investment.client.last_name}</p>
        </div>

        <!-- Client Information Section -->
        <div class="section-card" style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
          <div class="section-header" style="background: #f8f9fa; padding: 12px 20px; border-bottom: 1px solid #e0e0e0;">
            <h4 style="margin: 0; color: #1a1a1a; font-size: 16px; font-weight: 600;">Client Information</h4>
          </div>
          <div class="section-content" style="padding: 20px;">
            <table style="width: 100%; font-size: 12px; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151; width: 35%;">Primary Applicant</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.client.first_name} ${investment.client.last_name}</td>
              </tr>
              ${investment.client.email ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Email</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.client.email}</td>
              </tr>` : ''}
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Mobile</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.client.mobile_number}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Address</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.client.address}, ${investment.client.city}, ${investment.client.state} - ${investment.client.pincode}</td>
              </tr>
              ${investment.primary_applicant_cif_id ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">CIF ID</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.primary_applicant_cif_id}</td>
              </tr>` : ''}
              ${investment.secondary_applicant ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Secondary Applicant</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.secondary_applicant.first_name} ${investment.secondary_applicant.last_name}</td>
              </tr>` : ''}
              ${investment.nominee ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Nominee</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.nominee.name} (${investment.nominee.relation})</td>
              </tr>` : ''}
            </table>
          </div>
        </div>

        <!-- Investment Summary Section -->
        <div class="section-card" style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
          <div class="section-header" style="background: #f8f9fa; padding: 12px 20px; border-bottom: 1px solid #e0e0e0;">
            <h4 style="margin: 0; color: #1a1a1a; font-size: 16px; font-weight: 600;">Investment Summary</h4>
          </div>
          <div class="section-content" style="padding: 20px;">
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px;">
              <div style="text-align: center; padding: 15px; background: #e0f2fe; border-radius: 8px; border: 1px solid #b3e5fc;">
                <div style="font-size: 24px; font-weight: 700; color: #0277bd; margin-bottom: 5px;">${formatAmountInIndianFormat(investment.amount)}</div>
                <div style="font-size: 12px; color: #666; font-weight: 500;">Principal Amount</div>
              </div>
              <div style="text-align: center; padding: 15px; background: #e8f5e8; border-radius: 8px; border: 1px solid #c8e6c9;">
                <div style="font-size: 24px; font-weight: 700; color: #2e7d32; margin-bottom: 5px;">${formatAmountInIndianFormat(investment.maturity_amount)}</div>
                <div style="font-size: 12px; color: #666; font-weight: 500;">Maturity Amount</div>
              </div>
              <div style="text-align: center; padding: 15px; background: #fff3e0; border-radius: 8px; border: 1px solid #ffcc02;">
                <div style="font-size: 24px; font-weight: 700; color: #f57c00; margin-bottom: 5px;">${formatAmountInIndianFormat(grossProfit)}</div>
                <div style="font-size: 12px; color: #666; font-weight: 500;">Gross Profit</div>
              </div>
            </div>
            
            <table style="width: 100%; font-size: 12px; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151; width: 35%;">Investment Date</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${formatDisplayDate(investment.investment_date)}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Start Date</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${formatDisplayDate(investment.start_date)}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Maturity Date</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${formatDisplayDate(investment.maturity_date)}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Status</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                  <span style="padding: 4px 12px; background: ${investment.status === 'active' ? '#dcfce7' : investment.status === 'matured' ? '#dbeafe' : '#f3f4f6'}; color: ${investment.status === 'active' ? '#166534' : investment.status === 'matured' ? '#1e40af' : '#374151'}; border-radius: 12px; font-size: 11px; font-weight: 600; text-transform: uppercase;">${investment.status}</span>
                </td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Days to Maturity</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: ${daysToMaturity > 0 ? '#1a1a1a' : '#dc2626'}; font-weight: 600;">${daysToMaturity > 0 ? `${daysToMaturity} days` : 'Matured'}</td>
              </tr>
            </table>
          </div>
        </div>

        <!-- Scheme Details Section -->
        <div class="section-card" style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
          <div class="section-header" style="background: #f8f9fa; padding: 12px 20px; border-bottom: 1px solid #e0e0e0;">
            <h4 style="margin: 0; color: #1a1a1a; font-size: 16px; font-weight: 600;">Scheme Details</h4>
          </div>
          <div class="section-content" style="padding: 20px;">
            <table style="width: 100%; font-size: 12px; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151; width: 35%;">Scheme Name</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a; font-weight: 600;">${investment.scheme_name}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Scheme Code</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.scheme_code}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Interest Rate</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #f57c00; font-weight: 600;">${investment.interest_rate}% per annum</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Interest Type</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a; text-transform: capitalize;">${investment.interest_type}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Tenure</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.tenure_months} months</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Lock-in Period</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.lock_in_period_months} months</td>
              </tr>
              ${investment.compounding_frequency ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Compounding Frequency</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a; text-transform: capitalize;">${investment.compounding_frequency}</td>
              </tr>` : ''}
            </table>
          </div>
        </div>

        <!-- Financial Analysis Section -->
        <div class="section-card" style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
          <div class="section-header" style="background: #f8f9fa; padding: 12px 20px; border-bottom: 1px solid #e0e0e0;">
            <h4 style="margin: 0; color: #1a1a1a; font-size: 16px; font-weight: 600;">Financial Analysis</h4>
          </div>
          <div class="section-content" style="padding: 20px;">
            <table style="width: 100%; font-size: 12px; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151; width: 35%;">Principal Amount</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #0277bd; font-weight: 600;">${formatAmountInIndianFormat(investment.amount)}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Maturity Amount</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #2e7d32; font-weight: 600;">${formatAmountInIndianFormat(investment.maturity_amount)}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Gross Profit</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #f57c00; font-weight: 600;">${formatAmountInIndianFormat(grossProfit)}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Profit Percentage</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #7c3aed; font-weight: 600;">${profitPercentage}%</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Average Monthly Return</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #dc2626; font-weight: 600;">${formatAmountInIndianFormat(monthlyReturn)}</td>
              </tr>
              ${investment.commission_percentage ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Commission Rate</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #1a1a1a;">${investment.commission_percentage}%</td>
              </tr>` : ''}
              ${investment.tds_amount > 0 ? `
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">TDS Amount</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #dc2626; font-weight: 600;">${formatAmountInIndianFormat(investment.tds_amount)}</td>
              </tr>` : ''}
              <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; font-weight: 600; color: #374151;">Net Profit</td>
                <td style="padding: 8px 0; border-bottom: 1px solid #f0f0f0; color: #2e7d32; font-weight: 700; font-size: 14px;">${formatAmountInIndianFormat(investment.actual_profit)}</td>
              </tr>
            </table>
          </div>
        </div>

        ${investment.remark ? `
        <!-- Remarks Section -->
        <div class="section-card" style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
          <div class="section-header" style="background: #f8f9fa; padding: 12px 20px; border-bottom: 1px solid #e0e0e0;">
            <h4 style="margin: 0; color: #1a1a1a; font-size: 16px; font-weight: 600;">Remarks</h4>
          </div>
          <div class="section-content" style="padding: 20px;">
            <p style="margin: 0; color: #374151; font-size: 12px; line-height: 1.6;">${investment.remark}</p>
          </div>
        </div>` : ''}
      `;

      await exportToPDF({
        title: 'Individual Investment Report',
        content: tableContent,
        filterSummary
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error",
        description: "Failed to generate PDF report",
        variant: "destructive",
      });
    }
  };

  const sendEmail = async () => {
    if (!investment?.client?.email) {
      toast({
        title: "No Email",
        description: "Client email is not available",
        variant: "destructive",
      });
      return;
    }

    setSendingEmail(true);
    try {
      // Choose function based on investment status
      const functionName = investment.status === 'matured' ? 'send-maturity-confirmation' : 'send-investment-email';
      const emailType = investment.status === 'matured' ? 'maturity confirmation' : 'investment details';

      const isLocal = window.location.hostname === "localhost" ||
        window.location.hostname === "127.0.0.1";

      // Get service role key from environment variable or use fallback
      const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY ||
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU";

      let response;
      if (isLocal) {
        // Call local edge function directly
        response = await fetch(`${import.meta.env.VITE_SUPABASE_URL || "http://localhost:54321"}/functions/v1/${functionName}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${serviceRoleKey}`
          },
          body: JSON.stringify({
            investmentId: investment.id,
            clientEmail: investment.client.email,
            clientName: `${investment.client.first_name} ${investment.client.last_name}`
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText || "Local function error");
        }
      } else {
        // Call Supabase hosted edge function via SDK
        const { data, error } = await supabase.functions.invoke(functionName, {
          body: {
            investmentId: investment.id,
            clientEmail: investment.client.email,
            clientName: `${investment.client.first_name} ${investment.client.last_name}`
          }
        });

        if (error) throw error;
        response = { json: async () => data };
      }

      const data = await response.json();

      if (data?.success) {
        toast({
          title: "Email Sent",
          description: `${emailType.charAt(0).toUpperCase() + emailType.slice(1)} sent via SendGrid`,
        });
      } else {
        toast({
          title: "Email Failed",
          description: data?.message || "SendGrid send failed",
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error("Email send error:", error);
      toast({
        title: "Email Failed",
        description: error.message || "Unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setSendingEmail(false);
    }
  };

  const sendWhatsApp = async () => {
    if (!investment?.client?.mobile_number) {
      toast({
        title: "No Mobile Number",
        description: "Client mobile number is not available",
        variant: "destructive",
      });
      return;
    }

    if (!notificationSettings.whatsapp_enabled) {
      toast({
        title: "WhatsApp Disabled",
        description: "WhatsApp notifications are disabled in settings",
        variant: "destructive",
      });
      return;
    }

    setSendingWhatsApp(true);
    try {
      const message = `
*Investment Details*

Dear ${investment.client.first_name} ${investment.client.last_name},

Here are your investment details:

*Scheme:* ${investment.scheme_name} (${investment.scheme_code})
*Investment Amount:* ₹${investment.amount.toLocaleString()}
*Investment Date:* ${new Date(investment.investment_date).toLocaleDateString('en-IN')}
*Maturity Date:* ${new Date(investment.maturity_date).toLocaleDateString('en-IN')}
*Maturity Amount:* ₹${investment.maturity_amount.toLocaleString()}
*Interest Rate:* ${investment.interest_rate}% p.a.
*Status:* ${investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}

If you have any questions, please contact us.

Best regards,
Investment Management Team
      `.trim();

      let mobileNumber = investment.client.mobile_number.replace(/\D/g, '');

      if (mobileNumber.length === 10) {
        mobileNumber = '91' + mobileNumber;
      } else if (mobileNumber.startsWith('91') && mobileNumber.length === 12) {
        // Already has country code
      } else if (mobileNumber.startsWith('+91')) {
        mobileNumber = mobileNumber.substring(3);
        mobileNumber = '91' + mobileNumber;
      }

      const encodedMessage = encodeURIComponent(message);
      const whatsappUrl = `https://wa.me/${mobileNumber}?text=${encodedMessage}`;

      window.open(whatsappUrl, '_blank');

      toast({
        title: "WhatsApp Opened",
        description: "WhatsApp has been opened with the investment details. Please send the message.",
      });
    } catch (error) {
      console.error('Error opening WhatsApp:', error);
      toast({
        title: "WhatsApp Failed",
        description: "Failed to open WhatsApp. Please check the mobile number.",
        variant: "destructive",
      });
    } finally {
      setSendingWhatsApp(false);
    }
  };

  const sendSMS = async () => {
    if (!investment?.client?.mobile_number) {
      toast({
        title: "No Mobile Number",
        description: "Client mobile number is not available",
        variant: "destructive",
      });
      return;
    }

    if (!notificationSettings.sms_enabled) {
      toast({
        title: "SMS Disabled",
        description: "SMS notifications are disabled in settings",
        variant: "destructive",
      });
      return;
    }

    setSendingSMS(true);
    try {
      // Format mobile number for international format
      let mobileNumber = investment.client.mobile_number.replace(/\D/g, '');
      if (mobileNumber.length === 10) {
        mobileNumber = '+91' + mobileNumber;
      } else if (!mobileNumber.startsWith('+')) {
        mobileNumber = '+' + mobileNumber;
      }

      //       const message = `Investment Details

      // Dear ${investment.client.first_name} ${investment.client.last_name},

      // Your investment details:

      // Scheme: ${investment.scheme_name} (${investment.scheme_code})
      // Investment Amount: ₹${investment.amount.toLocaleString()}
      // Investment Date: ${new Date(investment.investment_date).toLocaleDateString('en-IN')}
      // Maturity Date: ${new Date(investment.maturity_date).toLocaleDateString('en-IN')}
      // Maturity Amount: ₹${investment.maturity_amount.toLocaleString()}
      // Interest Rate: ${investment.interest_rate}% p.a.
      // Status: ${investment.status.charAt(0).toUpperCase() + investment.status.slice(1)}

      // Thank you for investing with us!

      // Best regards,
      // Investment Team`.trim();
      const message = `Investment Details

Dear ${investment.client.first_name} ${investment.client.last_name},

Your investment Is Successfully Done`.trim();

      // Get Twilio configuration from database
      const { data: twilioSettings } = await supabase
        .from('notification_settings')
        .select('twilio_account_sid, twilio_auth_token, twilio_phone_number')
        .single();

      const accountSid = twilioSettings?.twilio_account_sid;
      const authToken = twilioSettings?.twilio_auth_token;
      const fromNumber = twilioSettings?.twilio_phone_number;

      if (!accountSid || !authToken || !fromNumber) {
        throw new Error('Twilio configuration missing in database settings.');
      }

      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`, {
        method: 'POST',
        headers: {
          'Authorization': 'Basic ' + btoa(`${accountSid}:${authToken}`),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          To: mobileNumber,
          From: fromNumber,
          Body: message
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send SMS');
      }

      const result = await response.json();

      toast({
        title: "SMS Sent Successfully",
        description: `Investment details sent to ${mobileNumber}`,
      });

    } catch (error) {
      console.error('Error sending SMS:', error);
      toast({
        title: "SMS Failed",
        description: error.message || "Failed to send SMS. Please check your configuration.",
        variant: "destructive",
      });
    } finally {
      setSendingSMS(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center ">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading investment details...</p>
        </div>
      </div>
    );
  }

  if (!investment) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Investment not found</h2>
          <Button onClick={() => navigate('/investments')} className="bg-blue-600 hover:bg-blue-700">
            Back to Investments
          </Button>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800 border-green-200',
      matured: 'bg-blue-100 text-blue-800 border-blue-200',
      withdrawn: 'bg-gray-100 text-gray-800 border-gray-200',
      transferred: 'bg-purple-100 text-purple-800 border-purple-200',
      reinvested: 'bg-orange-100 text-orange-800 border-orange-200',
    };

    return (
      <Badge variant="outline" className={`${colors[status as keyof typeof colors]} font-medium`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const grossProfit = investment.maturity_amount - investment.amount;

  return (
    <div className="min-h-screen bg-gradient-to-br ">
      <div className="space-y-6 mx-auto p-4 sm:p-6 lg:p-2">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => {
                  const state = location.state;
                  if (state?.from === 'client-detail' && state?.clientId) {
                    navigate(`/clients/${state.clientId}`);
                  } else {
                    navigate('/investments');
                  }
                }}
                className="hover:bg-white/80"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Investment Details</h1>
                <p className="text-gray-600 mt-1">
                  {investment.client.first_name} {investment.client.last_name} • {investment.scheme_name}
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                onClick={generatePDFReport}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <FileDown className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
              <Button
                onClick={sendEmail}
                disabled={!investment?.client?.email || sendingEmail}
                variant="outline"
                className="border-blue-200 hover:bg-blue-50"
              >
                <Mail className="h-4 w-4 mr-2" />
                {sendingEmail ? 'Sending...' : 'Email'}
              </Button>
              <Button
                onClick={sendSMS}
                disabled={!investment?.client?.mobile_number || sendingSMS || !notificationSettings.sms_enabled}
                variant="outline"
                className="border-green-200 hover:bg-green-50"
              >
                <Phone className="h-4 w-4 mr-2" />
                {sendingSMS ? 'Sending...' : 'SMS'}
              </Button>
              <Button
                onClick={sendWhatsApp}
                disabled={!investment?.client?.mobile_number || sendingWhatsApp || !notificationSettings.whatsapp_enabled}
                variant="outline"
                className="border-green-200 hover:bg-green-50"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {sendingWhatsApp ? 'Opening...' : 'WhatsApp'}
              </Button>
              <Button
                onClick={() => navigate(`/investments/${investment?.id}/edit`)}
                variant="outline"
                className="border-gray-200 hover:bg-gray-50"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Left Column - Client & Investment Info */}
          <div className="xl:col-span-2 space-y-6">
            {/* Investment Overview Card */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    Investment Overview
                  </CardTitle>
                  {getStatusBadge(investment.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <IndianRupee className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Investment Amount</p>
                    <p className="text-2xl font-bold text-gray-900">₹{investment.amount.toLocaleString()}</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Maturity Amount</p>
                    <p className="text-2xl font-bold text-green-600">₹{investment.maturity_amount.toLocaleString()}</p>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <Percent className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Interest Rate</p>
                    <p className="text-2xl font-bold text-orange-600">{investment.interest_rate}%</p>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      <span>Investment Date</span>
                    </div>
                    <p className="font-medium">{new Date(investment.investment_date).toLocaleDateString()}</p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Maturity Date</span>
                    </div>
                    <p className="font-medium">{new Date(investment.maturity_date).toLocaleDateString()}</p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <FileText className="h-4 w-4" />
                      <span>Scheme</span>
                    </div>
                    <p className="font-medium">{investment.scheme_name}</p>
                    <p className="text-sm text-gray-500">{investment.scheme_code}</p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <TrendingUp className="h-4 w-4" />
                      <span>Type</span>
                    </div>
                    <p className="font-medium capitalize">{investment.investment_type}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Client Information Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <ClientDetailsCard
                client={investment.client}
                title="Primary Applicant"
                cifId={investment.primary_applicant_cif_id}
                isEditable={false}
              />

              {investment.secondary_applicant && (
                <ClientDetailsCard
                  client={investment.secondary_applicant}
                  title="Secondary Applicant"
                  cifId={investment.secondary_applicant_cif_id}
                  isEditable={false}
                />
              )}
            </div>

            {/* Nominee Information */}
            {investment.nominee && (
              <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Nominee Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">
                      {investment.nominee.name} ({investment.nominee.relation})
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Financial Details */}
          <div className="space-y-6">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <IndianRupee className="h-5 w-5 text-green-600" />
                  Financial Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Principal Amount</span>
                  <span className="font-semibold text-gray-900">₹{investment.amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Maturity Amount</span>
                  <span className="font-semibold text-green-700">₹{investment.maturity_amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Gross Profit</span>
                  <span className="font-semibold text-orange-700">₹{grossProfit.toLocaleString()}</span>
                </div>
                {investment.commission_percentage ? (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Commission Rate</span>
                    <span className="font-semibold text-gray-900">{investment.commission_percentage}%</span>
                  </div>
                ) : null}
                {investment.tds_amount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">TDS Amount</span>
                    <span className="font-semibold text-gray-900">₹{investment.tds_amount.toLocaleString()}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Net Profit</span>
                  <span className="font-semibold text-blue-700">₹{investment.actual_profit.toLocaleString()}</span>
                </div>
                {investment.remark && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Remarks</span>
                    <span className="text-gray-900">{investment.remark}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Scheme Details Card */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Scheme Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Scheme Name</span>
                  <span className="font-semibold text-gray-900">{investment.scheme_name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Scheme Code</span>
                  <span className="font-semibold text-gray-900">{investment.scheme_code}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Interest Rate</span>
                  <span className="font-semibold text-orange-700">{investment.interest_rate}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Interest Type</span>
                  <span className="font-semibold text-gray-900">{investment.interest_type}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Tenure</span>
                  <span className="font-semibold text-gray-900">{investment.tenure_months} months</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Lock-in Period</span>
                  <span className="font-semibold text-gray-900">{investment.lock_in_period_months} months</span>
                </div>
                {investment.compounding_frequency && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Compounding</span>
                    <span className="font-semibold text-gray-900">{investment.compounding_frequency}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Payout Status</span>
                  <span className="font-semibold text-gray-900">{investment.payout_status}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestmentDetail;
