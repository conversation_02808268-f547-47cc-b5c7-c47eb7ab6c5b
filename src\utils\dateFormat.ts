
// Utility function to format dates in Indian format (dd-mm-yyyy)
export const formatDateToIndian = (dateString: string | Date): string => {
  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    return '';
  }
  
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}-${month}-${year}`;
};

// Format date for display in tables and UI
export const formatDisplayDate = (dateString: string | Date): string => {
  return formatDateToIndian(dateString);
};

// Export formatDate as alias for formatDisplayDate to maintain compatibility
export const formatDate = formatDisplayDate;
