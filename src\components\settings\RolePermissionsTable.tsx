
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

interface Role {
  id: string;
  name: string;
  description: string;
}

interface RolePermission {
  id: string;
  role_id: string;
  module: string;
  can_view: boolean;
  can_add: boolean;
  can_edit: boolean;
  can_delete: boolean;
}

interface RolePermissionsTableProps {
  roles: Role[];
  permissions: RolePermission[];
  modules: string[];
  onPermissionUpdate: (roleId: string, module: string, permissionType: string, value: boolean) => void;
}

const RolePermissionsTable: React.FC<RolePermissionsTableProps> = ({
  roles,
  permissions,
  modules,
  onPermissionUpdate
}) => {
  const getPermissionValue = (roleId: string, module: string, permissionType: string): boolean => {
    const permission = permissions.find(p => p.role_id === roleId && p.module === module);
    return permission ? permission[permissionType as keyof RolePermission] as boolean : false;
  };

  const permissionTypes = [
    { key: 'can_view', label: 'View', color: 'bg-blue-100 text-blue-800' },
    { key: 'can_add', label: 'Add', color: 'bg-green-100 text-green-800' },
    { key: 'can_edit', label: 'Edit', color: 'bg-yellow-100 text-yellow-800' },
    { key: 'can_delete', label: 'Delete', color: 'bg-red-100 text-red-800' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Role Permissions Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {roles.map(role => (
            <div key={role.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{role.name}</h3>
                  {role.description && (
                    <p className="text-sm text-gray-600">{role.description}</p>
                  )}
                </div>
                <Badge variant="outline">{role.name}</Badge>
              </div>
              
              <div className="grid gap-4">
                {modules.map(module => (
                  <div key={module} className="border rounded-md p-3 bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 capitalize">{module}</h4>
                      <div className="flex gap-2">
                        {permissionTypes.map(permType => (
                          <Badge
                            key={permType.key}
                            variant="secondary"
                            className={`text-xs ${
                              getPermissionValue(role.id, module, permType.key) 
                                ? permType.color 
                                : 'bg-gray-200 text-gray-600'
                            }`}
                          >
                            {permType.label}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {permissionTypes.map(permType => (
                        <div key={permType.key} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${role.id}-${module}-${permType.key}`}
                            checked={getPermissionValue(role.id, module, permType.key)}
                            onCheckedChange={(checked) => 
                              onPermissionUpdate(role.id, module, permType.key, checked as boolean)
                            }
                          />
                          <label
                            htmlFor={`${role.id}-${module}-${permType.key}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {permType.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RolePermissionsTable;
