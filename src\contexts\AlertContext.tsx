import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface AlertContextType {
  unreadCount: number;
  markAsRead: () => void;
  refreshAlerts: () => void;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const useAlerts = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlerts must be used within an AlertProvider');
  }
  return context;
};

interface AlertProviderProps {
  children: ReactNode;
}

export const AlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [lastChecked, setLastChecked] = useState<string | null>(null);

  useEffect(() => {
    // Get last checked time from localStorage
    const stored = localStorage.getItem('alerts_last_checked');
    setLastChecked(stored);
    
    fetchUnreadCount();
    
    // Set up real-time subscription for new alerts
    const subscription = supabase
      .channel('alerts')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'alerts' },
        () => {
          fetchUnreadCount();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchUnreadCount = async () => {
    try {
      const stored = localStorage.getItem('alerts_last_checked');
      const lastCheckedTime = stored || new Date(0).toISOString();

      const { data, error } = await supabase
        .from('alerts')
        .select('id')
        .gt('created_at', lastCheckedTime);

      if (error) throw error;
      setUnreadCount(data?.length || 0);
    } catch (error) {
      console.error('Error fetching unread alerts:', error);
    }
  };

  const markAsRead = () => {
    const now = new Date().toISOString();
    localStorage.setItem('alerts_last_checked', now);
    setLastChecked(now);
    setUnreadCount(0);
  };

  const refreshAlerts = () => {
    fetchUnreadCount();
  };

  return (
    <AlertContext.Provider value={{ unreadCount, markAsRead, refreshAlerts }}>
      {children}
    </AlertContext.Provider>
  );
};