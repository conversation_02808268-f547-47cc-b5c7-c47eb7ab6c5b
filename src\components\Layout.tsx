import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { usePermissions } from '@/hooks/usePermissions';
import { useAlerts } from '@/contexts/AlertContext';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { LayoutDashboard, Users, Briefcase, FileText, Calculator, Settings, Bell, LogOut, User, Menu, X, CreditCard, Wallet } from 'lucide-react';
const Layout: React.FC = () => {
  const {
    user,
    userRole,
    signOut
  } = useAuth();
  const {
    canViewModule,
    loading: permLoading
  } = usePermissions();
  const {
    unreadCount,
    markAsRead
  } = useAlerts();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const allMenuItems = [{
    label: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard',
    module: 'dashboard'
  }, {
    label: 'Clients',
    icon: Users,
    path: '/clients',
    module: 'clients'
  }, {
    label: 'Accounts',
    icon: Wallet,
    path: '/accounts',
    module: 'accounts'
  },
  {
    label: 'Investments',
    icon: Briefcase,
    path: '/investments',
    module: 'investments'
  }, {
    label: 'Schemes',
    icon: FileText,
    path: '/schemes',
    module: 'schemes'
  }, {
    label: 'Transactions',
    icon: CreditCard,
    path: '/transactions',
    module: 'transactions'
  }, {
    label: 'Calculator',
    icon: Calculator,
    path: '/calculator',
    module: 'calculator'
  }, {
    label: 'Reports',
    icon: FileText,
    path: '/reports',
    module: 'reports'
  }, {
    label: 'Alerts',
    icon: Bell,
    path: '/alerts',
    module: 'alerts'
  }, {
    label: 'Settings',
    icon: Settings,
    path: '/settings',
    module: 'settings'
  }];
  const menuItems = allMenuItems.filter(item => {
    // Admin can see everything
    if (userRole === 'Admin') return true;
    // For other roles, check permissions
    return canViewModule(item.module);
  });
  const handleSignOut = async () => {
    await signOut();
    navigate('/auth');
  };
  const handleMenuClick = (path: string) => {
    navigate(path);
    setSidebarOpen(false); // Close mobile sidebar after navigation
  };
  if (permLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p>Loading permissions...</p>
      </div>
    </div>;
  }
  return <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
    {/* Header - Fixed at top */}
    <header className="bg-white shadow-sm border-b h-16 flex-shrink-0 z-50">
      <div className="flex items-center justify-between px-4 sm:px-6 h-full">
        <div className="flex items-center gap-x-0 mx-0">
          {/* Mobile menu button */}
          <Button variant="ghost" size="sm" className="md:hidden" onClick={() => setSidebarOpen(!sidebarOpen)}>
            {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>

          {/* Logo */}
          <div className="flex items-center">
            <img alt="Investment Portal" src="/lovable-uploads/ab651bb9-5504-40da-ab30-1bd5fd7fdc5a.png" className="h-12 object-cover" />
            <img alt="Investment Portal" src="/lovable-uploads/e26f8b82-a2a2-46cc-a26e-fcf66ed47997.png" className="h-8 object-cover" />

          </div>
        </div>

        <div className="flex items-center gap-2 sm:gap-4">
          <div className="hidden sm:block">
            <span className="text-xs sm:text-sm text-gray-600 truncate max-w-32 sm:max-w-none">
              Welcome, {user?.email} ({userRole})
            </span>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <User className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white border shadow-lg z-50">
              {canViewModule('settings') && <DropdownMenuItem onClick={() => navigate('/settings')}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>}
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>

    <div className="flex flex-1 overflow-hidden">
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={() => setSidebarOpen(false)} />}

      {/* Sidebar - Fixed position, full height */}
      <aside className={`
          fixed md:static top-16 md:top-0 left-0 z-50 
          w-64 h-full md:h-auto bg-white shadow-sm border-r flex-shrink-0
          transform transition-transform duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          md:flex md:flex-col overflow-y-auto
        `}>
        <nav className="p-4 h-full">
          <ul className="space-y-1 sm:space-y-2">
            {menuItems.map(item => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              return <li key={item.path}>
                <Button variant={isActive ? "default" : "ghost"} className="w-full justify-start text-sm sm:text-base py-2 sm:py-3 relative" onClick={() => {
                  if (item.path === '/alerts') {
                    markAsRead();
                  }
                  handleMenuClick(item.path);
                }}>
                  <Icon className="h-4 w-4 mr-2 sm:mr-3" />
                  {item.label}
                  {item.path === '/alerts' && unreadCount > 0 && <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </span>}
                </Button>
              </li>;
            })}
          </ul>
        </nav>
      </aside>

      {/* Main Content - Scrollable area */}
      <main className="flex-1 overflow-y-auto h-full">
        <div className="p-3 sm:p-4 md:p-6 min-h-full">
          <Outlet />
        </div>
      </main>
    </div>
  </div>;
};
export default Layout;